"use client";
import "./chunk-C6WWHQR7.js";
import "./chunk-N2GVGIUZ.js";
import "./chunk-UUVOTGVA.js";
import {
  createSvgIcon
} from "./chunk-T3XEQW4B.js";
import "./chunk-E4MPIUBI.js";
import "./chunk-APDN7BYG.js";
import "./chunk-X34AUYXR.js";
import "./chunk-VOMKMCJM.js";
import "./chunk-JMU42XZZ.js";
import "./chunk-ZDQEUMQY.js";
import "./chunk-4YCHEYSE.js";
import {
  require_jsx_runtime
} from "./chunk-OBYCLIUT.js";
import "./chunk-BQYK6RGN.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/@mui/icons-material/esm/ScreenShare.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var ScreenShare_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.11-.9-2-2-2H4c-1.11 0-2 .89-2 2v10c0 1.1.89 2 2 2H0v2h24v-2zm-7-3.53v-2.19c-2.78 0-4.61.85-6 2.72.56-2.67 2.11-5.33 6-5.87V7l4 3.73z"
}), "ScreenShare");
export {
  ScreenShare_default as default
};
//# sourceMappingURL=@mui_icons-material_ScreenShare.js.map
