import React, { useEffect, useRef, useState } from 'react'
import "../style/video.css"
import { Badge, IconButton, TextField } from '@mui/material';
import { Button } from '@mui/material';



const server_url = "https://localhost:8000";


var connections = {};

const  peerConfigConnections = {
    "iceServers": [
        {
          urls: "stun:stun.l.google.com:19302"
        }
      ]
}

export default function VideoMeet() {

    var socketRef = useRef();
    let socketIdRef = useRef();

    let localVideoRef = useRef();

    let [videoAvilable, setVideoAvailable] = useState(true);
    let [audioAvilable, setAudioAvailable] = useState(true);

    let [video, setVideo] = useState();

    let [audio, setAudio] = useState();

    let [screen, setScreen] = useState();
    let [showModal , setShowModal] = useState();
    let [screenAvailable, setScreenAvailable] = useState(true);

    let [messages, setMessages] = useState([]);
    let [message, setMessage] = useState("");

    let [newMessages, setNewMessages] = useState(0);

    let [askForUsername, setAskForUsername] = useState(true);
    let [username, setUsername] = useState("");
    let [videos, setVideos] = useState([]);


    const videoRef = useRef([]);

    // todo
// if(isChrome() === false){

// }

// useEffect(() => {
//   getPermissions();
// },[])

const getPermissions = async () => {
  try {
    const videoPermission = await navigator.mediaDevices.getUserMedia({
      video: true,
    })
    if(videoPermission){
      setVideoAvailable(true);
    }else{
      setVideoAvailable(false);
    }
    const audioPermission = await navigator.mediaDevices.getUserMedia({
      audio: true,
    })
    if(audioPermission){
      setAudioAvailable(true);
    }else{
      setAudioAvailable(false);
    }

    if(navigator.mediaDevices.getDisplayMedia){
      setScreenAvailable(true);
    }else{
      setScreenAvailable(false);
    }

    if(videoAvilable || audioAvilable){
      const userMediaStream = await navigator.mediaDevices.getUserMedia({
        video: videoAvilable,
        audio: audioAvilable,
      });

      if(userMediaStream){
        window.localStream = userMediaStream;
        if(localVideoRef.current){
          localVideoRef.current.srcObject = userMediaStream;
        }
      }

    }
  } catch (error) {
    console.log(error);
  }
}

let getUserMediaSuccess = (stream) => {

}

let getUserMedia = () => {
  if((video && videoAvilable) || (audio && audioAvilable)){
    navigator.mediaDevices.getUserMedia({
      video: video,
      audio: audio,
    }).then((get) => {
      window.localStream = stream;
      if(localVideoRef.current){
        localVideoRef.current.srcObject = stream;
      }
    }).then(() => {})
    .catch((err) => {
      console.log(err);
    })
  }else{
    try {
      let tracks = localVideoRef.current.srcObject.getTracks();
      tracks.forEach((track) => {
        track.stop();
      });
    } catch (error) {
      
    }
  }
}

useEffect(() => {
  getPermissions();
},[])

useEffect(() => {
  if(video !== undefined && audio !== undefined){
    getUserMedia();
  }
}, [video, audio])

let connectToSocketServer = () => {
  socketRef.current = io.connect(server_url, {secure: false});
}

let getMedia = () => {
  setVideo(videoAvilable);
  setAudio(audioAvilable);
  connectToSocketServer();
}

let connect = () => {
  setAskForUsername(false);
  getMedia;
}

  return (
    <div>
        {askForUsername === true ? 
        (
    <div>

        <h2>Enter into lobby</h2>
        <TextField id="outlined-basic" label="Username" value={username} onChange={e => setUsername(e.target.value)} variant="outlined" />
       <Button variant="contained" onClick={connect}>Connect</Button>

        

    </div>
        ) : <></>
    }

    </div>
  )
}
