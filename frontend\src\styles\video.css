.meetVideoContainer {
    position: relative;
    height: 100vh;
    background: rgb(1, 4, 48);
}

.meetUserVideo {
    position: absolute;
    bottom: 10vh;
    height: 20vh;
    width: auto;
    left: 0;
    border-radius: 20px;
}

.buttonContainers {
    position: absolute;
    width: 100vw;
    bottom: 0;
    text-align: center;
}

.buttonContainers svg {
    font-size: 3rem;
}

.conferenceView { 

    display: flex;
    
    padding: 10px;
    gap: 10px;
}

.conferenceView  video {
    width: 40vw;
    height: 20vh;
    min-width: 30vw;
    border-radius: 10px;
}

.chatRoom {
    position: absolute;
    height: 90vh;
    right: 0;
    background: white;
    border-radius: 10px;
    width: 30vw;
    padding-inline: 20px;
}

.chatContainer {
    position: relative;
    height: 100%;
}

.chattingDisplay {
    height: 70vh;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #ccc;
    margin-bottom: 10px;
    background-color: #f9f9f9;
}

.chattingDisplay p {
    margin: 5px 0;
    padding: 8px;
    background-color: #e3f2fd;
    border-radius: 8px;
    border-left: 3px solid #2196f3;
}

.chattingArea {
    position: absolute;
    bottom: 0;
    width: 100%;
    display: flex;
    gap: 10px;
    padding: 10px;
    background-color: white;
}

.chattingArea .MuiTextField-root {
    flex: 1;
}