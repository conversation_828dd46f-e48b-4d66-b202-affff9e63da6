{"version": 3, "sources": ["../../@mui/material/esm/FormControlLabel/FormControlLabel.js", "../../@mui/material/esm/FormControlLabel/formControlLabelClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useFormControl } from \"../FormControl/index.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Typography from \"../Typography/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport formControlLabelClasses, { getFormControlLabelUtilityClasses } from \"./formControlLabelClasses.js\";\nimport formControlState from \"../FormControl/formControlState.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disabled,\n    labelPlacement,\n    error,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', `labelPlacement${capitalize(labelPlacement)}`, error && 'error', required && 'required'],\n    label: ['label', disabled && 'disabled'],\n    asterisk: ['asterisk', error && 'error']\n  };\n  return composeClasses(slots, getFormControlLabelUtilityClasses, classes);\n};\nexport const FormControlLabelRoot = styled('label', {\n  name: 'MuiFormControlLabel',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${formControlLabelClasses.label}`]: styles.label\n    }, styles.root, styles[`labelPlacement${capitalize(ownerState.labelPlacement)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'inline-flex',\n  alignItems: 'center',\n  cursor: 'pointer',\n  // For correct alignment with the text.\n  verticalAlign: 'middle',\n  WebkitTapHighlightColor: 'transparent',\n  marginLeft: -11,\n  marginRight: 16,\n  // used for row presentation of radio/checkbox\n  [`&.${formControlLabelClasses.disabled}`]: {\n    cursor: 'default'\n  },\n  [`& .${formControlLabelClasses.label}`]: {\n    [`&.${formControlLabelClasses.disabled}`]: {\n      color: (theme.vars || theme).palette.text.disabled\n    }\n  },\n  variants: [{\n    props: {\n      labelPlacement: 'start'\n    },\n    style: {\n      flexDirection: 'row-reverse',\n      marginRight: -11\n    }\n  }, {\n    props: {\n      labelPlacement: 'top'\n    },\n    style: {\n      flexDirection: 'column-reverse'\n    }\n  }, {\n    props: {\n      labelPlacement: 'bottom'\n    },\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: ({\n      labelPlacement\n    }) => labelPlacement === 'start' || labelPlacement === 'top' || labelPlacement === 'bottom',\n    style: {\n      marginLeft: 16 // used for row presentation of radio/checkbox\n    }\n  }]\n})));\nconst AsteriskComponent = styled('span', {\n  name: 'MuiFormControlLabel',\n  slot: 'Asterisk'\n})(memoTheme(({\n  theme\n}) => ({\n  [`&.${formControlLabelClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n})));\n\n/**\n * Drop-in replacement of the `Radio`, `Switch` and `Checkbox` component.\n * Use this component if you want to display an extra label.\n */\nconst FormControlLabel = /*#__PURE__*/React.forwardRef(function FormControlLabel(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormControlLabel'\n  });\n  const {\n    checked,\n    className,\n    componentsProps = {},\n    control,\n    disabled: disabledProp,\n    disableTypography,\n    inputRef,\n    label: labelProp,\n    labelPlacement = 'end',\n    name,\n    onChange,\n    required: requiredProp,\n    slots = {},\n    slotProps = {},\n    value,\n    ...other\n  } = props;\n  const muiFormControl = useFormControl();\n  const disabled = disabledProp ?? control.props.disabled ?? muiFormControl?.disabled;\n  const required = requiredProp ?? control.props.required;\n  const controlProps = {\n    disabled,\n    required\n  };\n  ['checked', 'name', 'onChange', 'value', 'inputRef'].forEach(key => {\n    if (typeof control.props[key] === 'undefined' && typeof props[key] !== 'undefined') {\n      controlProps[key] = props[key];\n    }\n  });\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['error']\n  });\n  const ownerState = {\n    ...props,\n    disabled,\n    labelPlacement,\n    required,\n    error: fcs.error\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      ...componentsProps,\n      ...slotProps\n    }\n  };\n  const [TypographySlot, typographySlotProps] = useSlot('typography', {\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  let label = labelProp;\n  if (label != null && label.type !== Typography && !disableTypography) {\n    label = /*#__PURE__*/_jsx(TypographySlot, {\n      component: \"span\",\n      ...typographySlotProps,\n      className: clsx(classes.label, typographySlotProps?.className),\n      children: label\n    });\n  }\n  return /*#__PURE__*/_jsxs(FormControlLabelRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other,\n    children: [/*#__PURE__*/React.cloneElement(control, controlProps), required ? /*#__PURE__*/_jsxs(\"div\", {\n      children: [label, /*#__PURE__*/_jsxs(AsteriskComponent, {\n        ownerState: ownerState,\n        \"aria-hidden\": true,\n        className: classes.asterisk,\n        children: [\"\\u2009\", '*']\n      })]\n    }) : label]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? FormControlLabel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component appears selected.\n   */\n  checked: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   * @deprecated use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  componentsProps: PropTypes.shape({\n    typography: PropTypes.object\n  }),\n  /**\n   * A control element. For instance, it can be a `Radio`, a `Switch` or a `Checkbox`.\n   */\n  control: PropTypes.element.isRequired,\n  /**\n   * If `true`, the control is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is rendered as it is passed without an additional typography node.\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * A text or an element to be used in an enclosing label element.\n   */\n  label: PropTypes.node,\n  /**\n   * The position of the label.\n   * @default 'end'\n   */\n  labelPlacement: PropTypes.oneOf(['bottom', 'end', 'start', 'top']),\n  /**\n   * @ignore\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    typography: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    typography: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default FormControlLabel;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFormControlLabelUtilityClasses(slot) {\n  return generateUtilityClass('MuiFormControlLabel', slot);\n}\nconst formControlLabelClasses = generateUtilityClasses('MuiFormControlLabel', ['root', 'labelPlacementStart', 'labelPlacementTop', 'labelPlacementBottom', 'disabled', 'label', 'error', 'required', 'asterisk']);\nexport default formControlLabelClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,kCAAkC,MAAM;AACtD,SAAO,qBAAqB,uBAAuB,IAAI;AACzD;AACA,IAAM,0BAA0B,uBAAuB,uBAAuB,CAAC,QAAQ,uBAAuB,qBAAqB,wBAAwB,YAAY,SAAS,SAAS,YAAY,UAAU,CAAC;AAChN,IAAO,kCAAQ;;;ADUf,yBAA2C;AAC3C,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,YAAY,YAAY,iBAAiB,mBAAW,cAAc,CAAC,IAAI,SAAS,SAAS,YAAY,UAAU;AAAA,IAC9H,OAAO,CAAC,SAAS,YAAY,UAAU;AAAA,IACvC,UAAU,CAAC,YAAY,SAAS,OAAO;AAAA,EACzC;AACA,SAAO,eAAe,OAAO,mCAAmC,OAAO;AACzE;AACO,IAAM,uBAAuB,eAAO,SAAS;AAAA,EAClD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC;AAAA,MACN,CAAC,MAAM,gCAAwB,KAAK,EAAE,GAAG,OAAO;AAAA,IAClD,GAAG,OAAO,MAAM,OAAO,iBAAiB,mBAAW,WAAW,cAAc,CAAC,EAAE,CAAC;AAAA,EAClF;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,QAAQ;AAAA;AAAA,EAER,eAAe;AAAA,EACf,yBAAyB;AAAA,EACzB,YAAY;AAAA,EACZ,aAAa;AAAA;AAAA,EAEb,CAAC,KAAK,gCAAwB,QAAQ,EAAE,GAAG;AAAA,IACzC,QAAQ;AAAA,EACV;AAAA,EACA,CAAC,MAAM,gCAAwB,KAAK,EAAE,GAAG;AAAA,IACvC,CAAC,KAAK,gCAAwB,QAAQ,EAAE,GAAG;AAAA,MACzC,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,gBAAgB;AAAA,IAClB;AAAA,IACA,OAAO;AAAA,MACL,eAAe;AAAA,MACf,aAAa;AAAA,IACf;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,gBAAgB;AAAA,IAClB;AAAA,IACA,OAAO;AAAA,MACL,eAAe;AAAA,IACjB;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,gBAAgB;AAAA,IAClB;AAAA,IACA,OAAO;AAAA,MACL,eAAe;AAAA,IACjB;AAAA,EACF,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,mBAAmB,WAAW,mBAAmB,SAAS,mBAAmB;AAAA,IACnF,OAAO;AAAA,MACL,YAAY;AAAA;AAAA,IACd;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,oBAAoB,eAAO,QAAQ;AAAA,EACvC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,CAAC,KAAK,gCAAwB,KAAK,EAAE,GAAG;AAAA,IACtC,QAAQ,MAAM,QAAQ,OAAO,QAAQ,MAAM;AAAA,EAC7C;AACF,EAAE,CAAC;AAMH,IAAM,mBAAsC,iBAAW,SAASA,kBAAiB,SAAS,KAAK;AAC7F,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,kBAAkB,CAAC;AAAA,IACnB;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,iBAAiB,eAAe;AACtC,QAAM,WAAW,gBAAgB,QAAQ,MAAM,YAAY,gBAAgB;AAC3E,QAAM,WAAW,gBAAgB,QAAQ,MAAM;AAC/C,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,EACF;AACA,GAAC,WAAW,QAAQ,YAAY,SAAS,UAAU,EAAE,QAAQ,SAAO;AAClE,QAAI,OAAO,QAAQ,MAAM,GAAG,MAAM,eAAe,OAAO,MAAM,GAAG,MAAM,aAAa;AAClF,mBAAa,GAAG,IAAI,MAAM,GAAG;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,QAAM,MAAM,iBAAiB;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,QAAQ,CAAC,OAAO;AAAA,EAClB,CAAC;AACD,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,IAAI;AAAA,EACb;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,yBAAyB;AAAA,IAC7B;AAAA,IACA,WAAW;AAAA,MACT,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,CAAC,gBAAgB,mBAAmB,IAAI,QAAQ,cAAc;AAAA,IAClE,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,QAAQ;AACZ,MAAI,SAAS,QAAQ,MAAM,SAAS,sBAAc,CAAC,mBAAmB;AACpE,gBAAqB,mBAAAC,KAAK,gBAAgB;AAAA,MACxC,WAAW;AAAA,MACX,GAAG;AAAA,MACH,WAAW,aAAK,QAAQ,OAAO,qBAAqB,SAAS;AAAA,MAC7D,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,aAAoB,mBAAAC,MAAM,sBAAsB;AAAA,IAC9C,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA;AAAA,IACA,GAAG;AAAA,IACH,UAAU,CAAoB,mBAAa,SAAS,YAAY,GAAG,eAAwB,mBAAAA,MAAM,OAAO;AAAA,MACtG,UAAU,CAAC,WAAoB,mBAAAA,MAAM,mBAAmB;AAAA,QACtD;AAAA,QACA,eAAe;AAAA,QACf,WAAW,QAAQ;AAAA,QACnB,UAAU,CAAC,KAAU,GAAG;AAAA,MAC1B,CAAC,CAAC;AAAA,IACJ,CAAC,IAAI,KAAK;AAAA,EACZ,CAAC;AACH,CAAC;AACD,OAAwC,iBAAiB,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1F,SAAS,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,iBAAiB,kBAAAA,QAAU,MAAM;AAAA,IAC/B,YAAY,kBAAAA,QAAU;AAAA,EACxB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,SAAS,kBAAAA,QAAU,QAAQ;AAAA;AAAA;AAAA;AAAA,EAI3B,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI7B,UAAU;AAAA;AAAA;AAAA;AAAA,EAIV,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,gBAAgB,kBAAAA,QAAU,MAAM,CAAC,UAAU,OAAO,SAAS,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA,EAIjE,MAAM,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,WAAW,kBAAAA,QAAU,MAAM;AAAA,IACzB,YAAY,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EACpE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,kBAAAA,QAAU,MAAM;AAAA,IACrB,YAAY,kBAAAA,QAAU;AAAA,EACxB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAItJ,OAAO,kBAAAA,QAAU;AACnB,IAAI;AACJ,IAAO,2BAAQ;", "names": ["FormControlLabel", "_jsx", "_jsxs", "PropTypes"]}