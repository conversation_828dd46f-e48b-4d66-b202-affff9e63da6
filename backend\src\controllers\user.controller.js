import httpStatus from "http-status";
import {User} from "../models/user.model.js";
import bcrypt, {hash} from "bcrypt";
import crypto from "crypto";
import {Meeting} from "../models/meeting.model.js";


const login = async(req, res) => {

    const {username, password} = req.body;

    if(!username || !password){
        return res.status(400).json({message: "Username and password are required"});
    }
    try {
        const user = await User.findOne({username});
        if(!user){
            return res.status(httpStatus.NOT_FOUND).json({message: "User not found"});
        }

        let isPasswordValid = await bcrypt.compare(password, user.password);

        if(isPasswordValid){
            let token = crypto.randomBytes(32).toString('hex');
            user.token = token;
            await user.save();
            return res.status(httpStatus.OK).json({token: token});
        }else{
            return res.status(httpStatus.UNAUTHORIZED).json({message: "Invalid credentials"});
        }
    } catch (e) {
        return res.status(500).json({message: "Something went wrong"});
    }
}

const register = async (req, res) => {
    const {name, username, password} = req.body;

    try{
        const existingUser = await User.findOne({username});
        if(existingUser){
            return res.status(httpStatus.FOUND).json({message: "User already exists"});
        }
        const hashedPassword = await bcrypt.hash(password, 10)

        const newUser = new User({
            name: name,
            username: username,
            password: hashedPassword
        })

        await newUser.save();

        res.status(httpStatus.CREATED).json({message: "User created successfully"})
    }catch(e){
        res.json({message: `Something went wrong ${e}`})
    }
}

const getUserHistory = async (req, res) => {
    const {token} = req.query;
    try{
        const user = await User.findOne({toekn: token});
        const meetings = await Meeting.find({user_id: user.username});

        res.json(meetings)
        // if(!user){
        //     return res.status(httpStatus.NOT_FOUND).json({message: "User not found"});
        // }
        // res.status(httpStatus.OK).json({history: user.history});
    }catch(e){
        res.status(httpStatus.INTERNAL_SERVER_ERROR).json({message: "Something went wrong"});
    }
}

const addToHistory = async (req, res) => {
    const {token, meeting_Code} = req.body;

    try{
        const user = await User.findOne({token: token});

        const newMeeting = new Meeting({
            user_id: user.username,
            meetingCode: meeting_code
        })

        await newMeeting.save();

        res.status(httpStatus.CREATED).json({message: "Meeting added to history"});

    }catch(e){
        res.status(httpStatus.INTERNAL_SERVER_ERROR).json({message: "Something went wrong"});
    }
}

export {login, register, getUserHistory, addToHistory};