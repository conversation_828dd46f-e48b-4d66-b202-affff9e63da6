import express from "express"
import {createServer} from "node:http"

import { Server } from "socket.io";
import mongoose from "mongoose";
import {connectToSocket} from "./controllers/socketManager.js"

import cors from "cors";
import userRoutes from "./routes/users.routes.js";

const app = express();
const server = createServer(app);
const io = connectToSocket(server);



app.set("port" , 8000)
app.use(cors());
app.use(express.json({limit: "40kb"}))
app.use(express.urlencoded({limit: "40kb" , extended: true}))

app.use("/api/v1/users", userRoutes)
// app.use("/api/v1/activities", activityRoutes)

const start = async () => {
    app.set("mongo_user")
    const connectionDb = await mongoose.connect("mongodb://localhost:27017/zoom")
    console.log("MONGODB CONNECTED");
    
    server.listen(app.get("port"),() => {
        console.log(`Server running on port ${app.get("port")}`);
        
    })
}

start()