
import { Route, BrowserRouter as Router, Routes } from 'react-router-dom'
import './App.css'
import Landing from './pages/landing'
import Auth from './pages/auth'
import { AuthProvider } from './contexts/AuthContext'
import VideoMeet from './pages/VideoMeet'

function App() {

  return (
    <>


     <Router>
    <AuthProvider>

      <Routes>
        <Route path='/' element={<Landing/>} />
        <Route path='/auth' element={<Auth/>} />
        <Route path='/:url' element={<VideoMeet/>} />
      </Routes>
    </AuthProvider>
     </Router>

    </>
  )
}

export default App
