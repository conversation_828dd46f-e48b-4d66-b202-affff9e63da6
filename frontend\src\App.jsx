
import { Route, BrowserRouter as Router, Routes } from 'react-router-dom'
import './App.css'
import Landing from './pages/landing'
import Auth from './pages/auth'
import { AuthProvider } from './contexts/AuthContext'
import VideoMeet from './pages/VideoMeet'
import Home from './pages/Home'
import History from './pages/History'

function App() {

  return (
    <>


     <Router>
    <AuthProvider>

      <Routes>
        <Route path='/' element={<Landing/>} />
        <Route path='/auth' element={<Auth/>} />
        <Route path='/home' element={<Home/>} />
        <Route path='/history' element={<History/>} />
        <Route path='/:url' element={<VideoMeet/>} />
      </Routes>
    </AuthProvider>
     </Router>

    </>
  )
}

export default App
