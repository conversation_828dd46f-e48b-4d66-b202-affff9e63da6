import React, { useContext, useEffect, useState } from 'react'
import { AuthContext } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom';
import Card from '@mui/material/Card';
import Box from '@mui/material/Box';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import HomeIcon from '@mui/icons-material/Home';

import { IconButton } from '@mui/material';
export default function History() {


    const { getHistory } = useContext(AuthContext);

    const [meetings, setMeetings] = useState([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState(null)


    const routeTo = useNavigate();

    useEffect(() => {
        const fetchHistory = async () => {
            try {
                setLoading(true);
                setError(null);
                const history = await getHistory();
                setMeetings(history);
            } catch (err) {
                console.error('Error fetching history:', err);
                setError('Failed to load meeting history');
            } finally {
                setLoading(false);
            }
        }

        fetchHistory();
    }, [getHistory])

    let formatDate = (dateString) => {

        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, "0");
        const month = (date.getMonth() + 1).toString().padStart(2, "0")
        const year = date.getFullYear();

        return `${day}/${month}/${year}`

    }

    return (
        <div style={{ padding: '20px' }}>

            <IconButton onClick={() => {
                routeTo("/home")
            }}>
                <HomeIcon />
            </IconButton >

            <Typography variant="h4" component="h1" gutterBottom>
                Meeting History
            </Typography>

            {loading && (
                <Typography>Loading meeting history...</Typography>
            )}

            {error && (
                <Typography color="error" style={{ marginBottom: '20px' }}>
                    {error}
                </Typography>
            )}

            {!loading && !error && (
                meetings.length !== 0 ? meetings.map((e, i) => {
                    return (
                        <Card key={i} variant="outlined" style={{ marginBottom: '15px', maxWidth: '600px' }}>
                            <CardContent>
                                <Typography sx={{ fontSize: 14 }} color="text.secondary" gutterBottom>
                                    Code: {e.meetingCode}
                                </Typography>

                                <Typography sx={{ mb: 1.5 }} color="text.secondary">
                                    Date: {formatDate(e.date)}
                                </Typography>
                            </CardContent>
                        </Card>
                    )
                }) : (
                    <Typography variant="body1" color="text.secondary">
                        No meeting history found.
                    </Typography>
                )
            )}

        </div>
    )
}