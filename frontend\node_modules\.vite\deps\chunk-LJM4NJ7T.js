import {
  require_prop_types
} from "./chunk-4YCHEYSE.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/@mui/utils/esm/refType/refType.js
var import_prop_types = __toESM(require_prop_types(), 1);
var refType = import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object]);
var refType_default = refType;

export {
  refType_default
};
//# sourceMappingURL=chunk-LJM4NJ7T.js.map
