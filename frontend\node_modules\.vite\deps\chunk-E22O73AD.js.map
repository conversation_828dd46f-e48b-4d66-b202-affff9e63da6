{"version": 3, "sources": ["../../@mui/material/esm/FormControl/useFormControl.js", "../../@mui/material/esm/FormControl/FormControlContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport FormControlContext from \"./FormControlContext.js\";\nexport default function useFormControl() {\n  return React.useContext(FormControlContext);\n}", "'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst FormControlContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  FormControlContext.displayName = 'FormControlContext';\n}\nexport default FormControlContext;"], "mappings": ";;;;;;;;AAEA,IAAAA,SAAuB;;;ACAvB,YAAuB;AAIvB,IAAM,qBAAwC,oBAAc,MAAS;AACrE,IAAI,MAAuC;AACzC,qBAAmB,cAAc;AACnC;AACA,IAAO,6BAAQ;;;ADNA,SAAR,iBAAkC;AACvC,SAAa,kBAAW,0BAAkB;AAC5C;", "names": ["React"]}