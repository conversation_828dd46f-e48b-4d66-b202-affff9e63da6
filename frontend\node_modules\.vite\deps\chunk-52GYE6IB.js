import {
  isFocusVisible
} from "./chunk-2RJCFLIP.js";
import {
  elementTypeAcceptingRef_default
} from "./chunk-JJ3BAJXL.js";
import {
  Typography_default
} from "./chunk-UZVUGHR3.js";
import {
  createSimplePaletteValueFilter
} from "./chunk-ZCSSBJ3Q.js";
import {
  capitalize_default
} from "./chunk-GIYARUHA.js";
import {
  memoTheme_default
} from "./chunk-2T4JMAIA.js";
import {
  useDefaultProps
} from "./chunk-7V2UCW3S.js";
import {
  alpha,
  clsx_default,
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses,
  getPath,
  require_prop_types,
  styled_default,
  useTheme
} from "./chunk-NTVVN6LG.js";
import {
  require_jsx_runtime
} from "./chunk-OBYCLIUT.js";
import {
  require_react
} from "./chunk-BQYK6RGN.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/@mui/material/esm/Link/Link.js
var React = __toESM(require_react(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);

// node_modules/@mui/material/esm/Link/linkClasses.js
function getLinkUtilityClass(slot) {
  return generateUtilityClass("MuiLink", slot);
}
var linkClasses = generateUtilityClasses("MuiLink", ["root", "underlineNone", "underlineHover", "underlineAlways", "button", "focusVisible"]);
var linkClasses_default = linkClasses;

// node_modules/@mui/material/esm/Link/getTextDecoration.js
var getTextDecoration = ({
  theme,
  ownerState
}) => {
  const transformedColor = ownerState.color;
  if ("colorSpace" in theme && theme.colorSpace) {
    const color2 = getPath(theme, `palette.${transformedColor}.main`) || getPath(theme, `palette.${transformedColor}`) || ownerState.color;
    return theme.alpha(color2, 0.4);
  }
  const color = getPath(theme, `palette.${transformedColor}.main`, false) || getPath(theme, `palette.${transformedColor}`, false) || ownerState.color;
  const channelColor = getPath(theme, `palette.${transformedColor}.mainChannel`) || getPath(theme, `palette.${transformedColor}Channel`);
  if ("vars" in theme && channelColor) {
    return `rgba(${channelColor} / 0.4)`;
  }
  return alpha(color, 0.4);
};
var getTextDecoration_default = getTextDecoration;

// node_modules/@mui/material/esm/Link/Link.js
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var v6Colors = {
  primary: true,
  secondary: true,
  error: true,
  info: true,
  success: true,
  warning: true,
  textPrimary: true,
  textSecondary: true,
  textDisabled: true
};
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    component,
    focusVisible,
    underline
  } = ownerState;
  const slots = {
    root: ["root", `underline${capitalize_default(underline)}`, component === "button" && "button", focusVisible && "focusVisible"]
  };
  return composeClasses(slots, getLinkUtilityClass, classes);
};
var LinkRoot = styled_default(Typography_default, {
  name: "MuiLink",
  slot: "Root",
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.root, styles[`underline${capitalize_default(ownerState.underline)}`], ownerState.component === "button" && styles.button];
  }
})(memoTheme_default(({
  theme
}) => {
  return {
    variants: [{
      props: {
        underline: "none"
      },
      style: {
        textDecoration: "none"
      }
    }, {
      props: {
        underline: "hover"
      },
      style: {
        textDecoration: "none",
        "&:hover": {
          textDecoration: "underline"
        }
      }
    }, {
      props: {
        underline: "always"
      },
      style: {
        textDecoration: "underline",
        "&:hover": {
          textDecorationColor: "inherit"
        }
      }
    }, {
      props: ({
        underline,
        ownerState
      }) => underline === "always" && ownerState.color !== "inherit",
      style: {
        textDecorationColor: "var(--Link-underlineColor)"
      }
    }, {
      props: ({
        underline,
        ownerState
      }) => underline === "always" && ownerState.color === "inherit",
      style: theme.colorSpace ? {
        textDecorationColor: theme.alpha("currentColor", 0.4)
      } : null
    }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter()).map(([color]) => ({
      props: {
        underline: "always",
        color
      },
      style: {
        "--Link-underlineColor": theme.alpha((theme.vars || theme).palette[color].main, 0.4)
      }
    })), {
      props: {
        underline: "always",
        color: "textPrimary"
      },
      style: {
        "--Link-underlineColor": theme.alpha((theme.vars || theme).palette.text.primary, 0.4)
      }
    }, {
      props: {
        underline: "always",
        color: "textSecondary"
      },
      style: {
        "--Link-underlineColor": theme.alpha((theme.vars || theme).palette.text.secondary, 0.4)
      }
    }, {
      props: {
        underline: "always",
        color: "textDisabled"
      },
      style: {
        "--Link-underlineColor": (theme.vars || theme).palette.text.disabled
      }
    }, {
      props: {
        component: "button"
      },
      style: {
        position: "relative",
        WebkitTapHighlightColor: "transparent",
        backgroundColor: "transparent",
        // Reset default value
        // We disable the focus ring for mouse, touch and keyboard users.
        outline: 0,
        border: 0,
        margin: 0,
        // Remove the margin in Safari
        borderRadius: 0,
        padding: 0,
        // Remove the padding in Firefox
        cursor: "pointer",
        userSelect: "none",
        verticalAlign: "middle",
        MozAppearance: "none",
        // Reset
        WebkitAppearance: "none",
        // Reset
        "&::-moz-focus-inner": {
          borderStyle: "none"
          // Remove Firefox dotted outline.
        },
        [`&.${linkClasses_default.focusVisible}`]: {
          outline: "auto"
        }
      }
    }]
  };
}));
var Link = React.forwardRef(function Link2(inProps, ref) {
  const props = useDefaultProps({
    props: inProps,
    name: "MuiLink"
  });
  const theme = useTheme();
  const {
    className,
    color = "primary",
    component = "a",
    onBlur,
    onFocus,
    TypographyClasses,
    underline = "always",
    variant = "inherit",
    sx,
    ...other
  } = props;
  const [focusVisible, setFocusVisible] = React.useState(false);
  const handleBlur = (event) => {
    if (!isFocusVisible(event.target)) {
      setFocusVisible(false);
    }
    if (onBlur) {
      onBlur(event);
    }
  };
  const handleFocus = (event) => {
    if (isFocusVisible(event.target)) {
      setFocusVisible(true);
    }
    if (onFocus) {
      onFocus(event);
    }
  };
  const ownerState = {
    ...props,
    color,
    component,
    focusVisible,
    underline,
    variant
  };
  const classes = useUtilityClasses(ownerState);
  return (0, import_jsx_runtime.jsx)(LinkRoot, {
    color,
    className: clsx_default(classes.root, className),
    classes: TypographyClasses,
    component,
    onBlur: handleBlur,
    onFocus: handleFocus,
    ref,
    ownerState,
    variant,
    ...other,
    sx: [...v6Colors[color] === void 0 ? [{
      color
    }] : [], ...Array.isArray(sx) ? sx : [sx]],
    style: {
      ...other.style,
      ...underline === "always" && color !== "inherit" && !v6Colors[color] && {
        "--Link-underlineColor": getTextDecoration_default({
          theme,
          ownerState
        })
      }
    }
  });
});
true ? Link.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: import_prop_types.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object,
  /**
   * @ignore
   */
  className: import_prop_types.default.string,
  /**
   * The color of the link.
   * @default 'primary'
   */
  color: import_prop_types.default.oneOfType([import_prop_types.default.oneOf(["primary", "secondary", "success", "error", "info", "warning", "textPrimary", "textSecondary", "textDisabled"]), import_prop_types.default.string]),
  /**
   * The component used for the root node.
   * Either a string to use a HTML element or a component.
   */
  component: elementTypeAcceptingRef_default,
  /**
   * @ignore
   */
  onBlur: import_prop_types.default.func,
  /**
   * @ignore
   */
  onFocus: import_prop_types.default.func,
  /**
   * @ignore
   */
  style: import_prop_types.default.object,
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object, import_prop_types.default.bool])), import_prop_types.default.func, import_prop_types.default.object]),
  /**
   * `classes` prop applied to the [`Typography`](https://mui.com/material-ui/api/typography/) element.
   */
  TypographyClasses: import_prop_types.default.object,
  /**
   * Controls when the link should have an underline.
   * @default 'always'
   */
  underline: import_prop_types.default.oneOf(["always", "hover", "none"]),
  /**
   * Applies the theme typography styles.
   * @default 'inherit'
   */
  variant: import_prop_types.default.oneOfType([import_prop_types.default.oneOf(["body1", "body2", "button", "caption", "h1", "h2", "h3", "h4", "h5", "h6", "inherit", "overline", "subtitle1", "subtitle2"]), import_prop_types.default.string])
} : void 0;
var Link_default = Link;

export {
  getLinkUtilityClass,
  linkClasses_default,
  Link_default
};
//# sourceMappingURL=chunk-52GYE6IB.js.map
