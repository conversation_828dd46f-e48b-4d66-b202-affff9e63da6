{"version": 3, "sources": ["../../@mui/material/esm/Avatar/Avatar.js", "../../@mui/material/esm/internal/svg-icons/Person.js", "../../@mui/material/esm/Avatar/avatarClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Person from \"../internal/svg-icons/Person.js\";\nimport { getAvatarUtilityClass } from \"./avatarClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant,\n    colorDefault\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, colorDefault && 'colorDefault'],\n    img: ['img'],\n    fallback: ['fallback']\n  };\n  return composeClasses(slots, getAvatarUtilityClass, classes);\n};\nconst AvatarRoot = styled('div', {\n  name: 'MuiAvatar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], ownerState.colorDefault && styles.colorDefault];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'relative',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  flexShrink: 0,\n  width: 40,\n  height: 40,\n  fontFamily: theme.typography.fontFamily,\n  fontSize: theme.typography.pxToRem(20),\n  lineHeight: 1,\n  borderRadius: '50%',\n  overflow: 'hidden',\n  userSelect: 'none',\n  variants: [{\n    props: {\n      variant: 'rounded'\n    },\n    style: {\n      borderRadius: (theme.vars || theme).shape.borderRadius\n    }\n  }, {\n    props: {\n      variant: 'square'\n    },\n    style: {\n      borderRadius: 0\n    }\n  }, {\n    props: {\n      colorDefault: true\n    },\n    style: {\n      color: (theme.vars || theme).palette.background.default,\n      ...(theme.vars ? {\n        backgroundColor: theme.vars.palette.Avatar.defaultBg\n      } : {\n        backgroundColor: theme.palette.grey[400],\n        ...theme.applyStyles('dark', {\n          backgroundColor: theme.palette.grey[600]\n        })\n      })\n    }\n  }]\n})));\nconst AvatarImg = styled('img', {\n  name: 'MuiAvatar',\n  slot: 'Img'\n})({\n  width: '100%',\n  height: '100%',\n  textAlign: 'center',\n  // Handle non-square image.\n  objectFit: 'cover',\n  // Hide alt text.\n  color: 'transparent',\n  // Hide the image broken icon, only works on Chrome.\n  textIndent: 10000\n});\nconst AvatarFallback = styled(Person, {\n  name: 'MuiAvatar',\n  slot: 'Fallback'\n})({\n  width: '75%',\n  height: '75%'\n});\nfunction useLoaded({\n  crossOrigin,\n  referrerPolicy,\n  src,\n  srcSet\n}) {\n  const [loaded, setLoaded] = React.useState(false);\n  React.useEffect(() => {\n    if (!src && !srcSet) {\n      return undefined;\n    }\n    setLoaded(false);\n    let active = true;\n    const image = new Image();\n    image.onload = () => {\n      if (!active) {\n        return;\n      }\n      setLoaded('loaded');\n    };\n    image.onerror = () => {\n      if (!active) {\n        return;\n      }\n      setLoaded('error');\n    };\n    image.crossOrigin = crossOrigin;\n    image.referrerPolicy = referrerPolicy;\n    image.src = src;\n    if (srcSet) {\n      image.srcset = srcSet;\n    }\n    return () => {\n      active = false;\n    };\n  }, [crossOrigin, referrerPolicy, src, srcSet]);\n  return loaded;\n}\nconst Avatar = /*#__PURE__*/React.forwardRef(function Avatar(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAvatar'\n  });\n  const {\n    alt,\n    children: childrenProp,\n    className,\n    component = 'div',\n    slots = {},\n    slotProps = {},\n    imgProps,\n    sizes,\n    src,\n    srcSet,\n    variant = 'circular',\n    ...other\n  } = props;\n  let children = null;\n  const ownerState = {\n    ...props,\n    component,\n    variant\n  };\n\n  // Use a hook instead of onError on the img element to support server-side rendering.\n  const loaded = useLoaded({\n    ...imgProps,\n    ...(typeof slotProps.img === 'function' ? slotProps.img(ownerState) : slotProps.img),\n    src,\n    srcSet\n  });\n  const hasImg = src || srcSet;\n  const hasImgNotFailing = hasImg && loaded !== 'error';\n  ownerState.colorDefault = !hasImgNotFailing;\n  // This issue explains why this is required: https://github.com/mui/material-ui/issues/42184\n  delete ownerState.ownerState;\n  const classes = useUtilityClasses(ownerState);\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: AvatarRoot,\n    externalForwardedProps: {\n      slots,\n      slotProps,\n      component,\n      ...other\n    },\n    ownerState\n  });\n  const [ImgSlot, imgSlotProps] = useSlot('img', {\n    className: classes.img,\n    elementType: AvatarImg,\n    externalForwardedProps: {\n      slots,\n      slotProps: {\n        img: {\n          ...imgProps,\n          ...slotProps.img\n        }\n      }\n    },\n    additionalProps: {\n      alt,\n      src,\n      srcSet,\n      sizes\n    },\n    ownerState\n  });\n  const [FallbackSlot, fallbackSlotProps] = useSlot('fallback', {\n    className: classes.fallback,\n    elementType: AvatarFallback,\n    externalForwardedProps: {\n      slots,\n      slotProps\n    },\n    shouldForwardComponentProp: true,\n    ownerState\n  });\n  if (hasImgNotFailing) {\n    children = /*#__PURE__*/_jsx(ImgSlot, {\n      ...imgSlotProps\n    });\n    // We only render valid children, non valid children are rendered with a fallback\n    // We consider that invalid children are all falsy values, except 0, which is valid.\n  } else if (!!childrenProp || childrenProp === 0) {\n    children = childrenProp;\n  } else if (hasImg && alt) {\n    children = alt[0];\n  } else {\n    children = /*#__PURE__*/_jsx(FallbackSlot, {\n      ...fallbackSlotProps\n    });\n  }\n  return /*#__PURE__*/_jsx(RootSlot, {\n    ...rootSlotProps,\n    children: children\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Avatar.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used in combination with `src` or `srcSet` to\n   * provide an alt attribute for the rendered `img` element.\n   */\n  alt: PropTypes.string,\n  /**\n   * Used to render icon or text elements inside the Avatar if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Elements/img#attributes) applied to the `img` element if the component is used to display an image.\n   * It can be used to listen for the loading error event.\n   * @deprecated Use `slotProps.img` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  imgProps: PropTypes.object,\n  /**\n   * The `sizes` attribute for the `img` element.\n   */\n  sizes: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    fallback: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    img: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    fallback: PropTypes.elementType,\n    img: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The `src` attribute for the `img` element.\n   */\n  src: PropTypes.string,\n  /**\n   * The `srcSet` attribute for the `img` element.\n   * Use this attribute for responsive image display.\n   */\n  srcSet: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The shape of the avatar.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rounded', 'square']), PropTypes.string])\n} : void 0;\nexport default Avatar;", "'use client';\n\nimport * as React from 'react';\nimport createSvgIcon from \"../../utils/createSvgIcon.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"\n}), 'Person');", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAvatarUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatar', slot);\n}\nconst avatarClasses = generateUtilityClasses('MuiAvatar', ['root', 'colorDefault', 'circular', 'rounded', 'square', 'img', 'fallback']);\nexport default avatarClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAA,SAAuB;AACvB,wBAAsB;;;ACDtB,YAAuB;AAMvB,yBAA4B;AAC5B,IAAO,iBAAQ,kBAA2B,mBAAAC,KAAK,QAAQ;AAAA,EACrD,GAAG;AACL,CAAC,GAAG,QAAQ;;;ACTL,SAAS,sBAAsB,MAAM;AAC1C,SAAO,qBAAqB,aAAa,IAAI;AAC/C;AACA,IAAM,gBAAgB,uBAAuB,aAAa,CAAC,QAAQ,gBAAgB,YAAY,WAAW,UAAU,OAAO,UAAU,CAAC;AACtI,IAAO,wBAAQ;;;AFMf,IAAAC,sBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,SAAS,gBAAgB,cAAc;AAAA,IACtD,KAAK,CAAC,KAAK;AAAA,IACX,UAAU,CAAC,UAAU;AAAA,EACvB;AACA,SAAO,eAAe,OAAO,uBAAuB,OAAO;AAC7D;AACA,IAAM,aAAa,eAAO,OAAO;AAAA,EAC/B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,WAAW,OAAO,GAAG,WAAW,gBAAgB,OAAO,YAAY;AAAA,EACjG;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY,MAAM,WAAW;AAAA,EAC7B,UAAU,MAAM,WAAW,QAAQ,EAAE;AAAA,EACrC,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,eAAe,MAAM,QAAQ,OAAO,MAAM;AAAA,IAC5C;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,cAAc;AAAA,IAChB;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,QAAQ,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,MAChD,GAAI,MAAM,OAAO;AAAA,QACf,iBAAiB,MAAM,KAAK,QAAQ,OAAO;AAAA,MAC7C,IAAI;AAAA,QACF,iBAAiB,MAAM,QAAQ,KAAK,GAAG;AAAA,QACvC,GAAG,MAAM,YAAY,QAAQ;AAAA,UAC3B,iBAAiB,MAAM,QAAQ,KAAK,GAAG;AAAA,QACzC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,YAAY,eAAO,OAAO;AAAA,EAC9B,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,WAAW;AAAA;AAAA,EAEX,WAAW;AAAA;AAAA,EAEX,OAAO;AAAA;AAAA,EAEP,YAAY;AACd,CAAC;AACD,IAAM,iBAAiB,eAAO,gBAAQ;AAAA,EACpC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AACV,CAAC;AACD,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,CAAC,QAAQ,SAAS,IAAU,gBAAS,KAAK;AAChD,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,OAAO,CAAC,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,cAAU,KAAK;AACf,QAAI,SAAS;AACb,UAAM,QAAQ,IAAI,MAAM;AACxB,UAAM,SAAS,MAAM;AACnB,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,UAAM,UAAU,MAAM;AACpB,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AACA,gBAAU,OAAO;AAAA,IACnB;AACA,UAAM,cAAc;AACpB,UAAM,iBAAiB;AACvB,UAAM,MAAM;AACZ,QAAI,QAAQ;AACV,YAAM,SAAS;AAAA,IACjB;AACA,WAAO,MAAM;AACX,eAAS;AAAA,IACX;AAAA,EACF,GAAG,CAAC,aAAa,gBAAgB,KAAK,MAAM,CAAC;AAC7C,SAAO;AACT;AACA,IAAM,SAA4B,kBAAW,SAASC,QAAO,SAAS,KAAK;AACzE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA,YAAY;AAAA,IACZ,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,GAAG;AAAA,EACL,IAAI;AACJ,MAAI,WAAW;AACf,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,EACF;AAGA,QAAM,SAAS,UAAU;AAAA,IACvB,GAAG;AAAA,IACH,GAAI,OAAO,UAAU,QAAQ,aAAa,UAAU,IAAI,UAAU,IAAI,UAAU;AAAA,IAChF;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,SAAS,OAAO;AACtB,QAAM,mBAAmB,UAAU,WAAW;AAC9C,aAAW,eAAe,CAAC;AAE3B,SAAO,WAAW;AAClB,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,CAAC,UAAU,aAAa,IAAI,QAAQ,QAAQ;AAAA,IAChD;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC,aAAa;AAAA,IACb,wBAAwB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,CAAC,SAAS,YAAY,IAAI,QAAQ,OAAO;AAAA,IAC7C,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb,wBAAwB;AAAA,MACtB;AAAA,MACA,WAAW;AAAA,QACT,KAAK;AAAA,UACH,GAAG;AAAA,UACH,GAAG,UAAU;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,CAAC,cAAc,iBAAiB,IAAI,QAAQ,YAAY;AAAA,IAC5D,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb,wBAAwB;AAAA,MACtB;AAAA,MACA;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,IAC5B;AAAA,EACF,CAAC;AACD,MAAI,kBAAkB;AACpB,mBAAwB,oBAAAC,KAAK,SAAS;AAAA,MACpC,GAAG;AAAA,IACL,CAAC;AAAA,EAGH,WAAW,CAAC,CAAC,gBAAgB,iBAAiB,GAAG;AAC/C,eAAW;AAAA,EACb,WAAW,UAAU,KAAK;AACxB,eAAW,IAAI,CAAC;AAAA,EAClB,OAAO;AACL,mBAAwB,oBAAAA,KAAK,cAAc;AAAA,MACzC,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACA,aAAoB,oBAAAA,KAAK,UAAU;AAAA,IACjC,GAAG;AAAA,IACH;AAAA,EACF,CAAC;AACH,CAAC;AACD,OAAwC,OAAO,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAShF,KAAK,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,WAAW,kBAAAA,QAAU,MAAM;AAAA,IACzB,UAAU,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,KAAK,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC3D,MAAM,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EAC9D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,kBAAAA,QAAU,MAAM;AAAA,IACrB,UAAU,kBAAAA,QAAU;AAAA,IACpB,KAAK,kBAAAA,QAAU;AAAA,IACf,MAAM,kBAAAA,QAAU;AAAA,EAClB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,KAAK,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,YAAY,WAAW,QAAQ,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAC3I,IAAI;AACJ,IAAO,iBAAQ;", "names": ["React", "_jsx", "import_jsx_runtime", "Avatar", "_jsx", "PropTypes"]}