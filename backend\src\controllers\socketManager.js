import { Server } from "socket.io"


let connections = {};
let messages = {};
let timeOnline = {};

export const connectToSocket = (server) => {
    const io = new Server(server,{
        cors:{
            origin: "*",
            methods: ["GET", "POST"],
            allowedHeaders: ["*"],
            credentials: true,
        }
});
    
    io.on("connection", (socket) => {
        
        console.log("new connection", socket.id)
        
        socket.on("join-call" , (path) => {

            if(connections[path] === undefined){
                connections[path] = [];
            }
            connections[path].push(socket.id)

            timeOnline[socket.id] = Date.now();

            // connections[path].forEa


            for(let a =0; a< connections[path].length; a++){
                io.to(connections[path][a]).emit("user-connected", socket.id , connections[path]);
                }

                if(message[path] !== undefined){
                   for (let a =0; a< message[path].length; a++){
                    io.to(socket.id).emit("chat-message", message[path][a]['data'],
                        message[path][a]['sender'], message[path][a]['socket-id-sender']
                    );
                   } 
            }
        })

        socket.on("signal", (toId, message) => {
            socket.to(toId).emit("singal", socket.id, message);
        })

        socket.on("chat-message" , (data, sender) => {
            const [matchingRoom , found] = Object.entries(connections).reduce(([room, isFound], [roomKey , roomValue]) => {
                if(!isFound && roomValue.includes(socket.id)){
                    return [roomKey, true];
                }
                return [room, isFound];
            
        } ,['', false]);

        if(found === true){
            if(messages[matchingRoom] === undefined){
                messages[matchingRoom] = [];
            }
            messages[matchingRoom].push({"data": data, "sender": sender, "socket-id-sender": socket.id 
            })
                console.log("messages", key, ":", sender, data)

                connections[machingRoom].forEach((elem) => {
                io.to(elem).emit("chat-message", data, sender, socket.id);
            });
        }
    })
        socket.on("disconnect", () => {
            var diffTime = Math.abs(timeOnline[socket.id] - Date.now());

            var key 

            for(const [k,v] of json.parse(JSON.stringify(Object.entries(connections)))){
               for(let a =0; a< v.length; a++){
                if(v[a] === socket.id){
                    key = k;
                    for(let a =0 ; a< connections[key].length; a++){
                        io.to(connections[key][a]).emit("user-disconnected", socket.id);
                    }

                    var index = connections[key].indexOf(socket.id);
                    connections[key].splice(index, 1);


                    if(connections[key].length === 0){
                        delete connections[key];
                        delete messages[key];
                    }
                }
               }
            }
        })
})
}
