{"version": 3, "sources": ["../../@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../../@babel/runtime/helpers/esm/setPrototypeOf.js", "../../@babel/runtime/helpers/esm/inheritsLoose.js", "../../react-transition-group/esm/Transition.js", "../../react-transition-group/esm/config.js", "../../react-transition-group/esm/utils/PropTypes.js", "../../react-transition-group/esm/TransitionGroupContext.js", "../../react-transition-group/esm/utils/reflow.js", "../../react-transition-group/esm/CSSTransition.js", "../../dom-helpers/esm/hasClass.js", "../../dom-helpers/esm/addClass.js", "../../dom-helpers/esm/removeClass.js", "../../react-transition-group/esm/ReplaceTransition.js", "../../@babel/runtime/helpers/esm/assertThisInitialized.js", "../../react-transition-group/esm/TransitionGroup.js", "../../react-transition-group/esm/utils/ChildMapping.js", "../../react-transition-group/esm/SwitchTransition.js", "../../@mui/utils/esm/useLazyRef/useLazyRef.js", "../../@mui/utils/esm/useOnMount/useOnMount.js", "../../@mui/utils/esm/useTimeout/useTimeout.js"], "sourcesContent": ["function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inheritsLoose(t, o) {\n  t.prototype = Object.create(o.prototype), t.prototype.constructor = t, setPrototypeOf(t, o);\n}\nexport { _inheritsLoose as default };", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport config from './config';\nimport { timeoutsShape } from './utils/PropTypes';\nimport TransitionGroupContext from './TransitionGroupContext';\nimport { forceReflow } from './utils/reflow';\nexport var UNMOUNTED = 'unmounted';\nexport var EXITED = 'exited';\nexport var ENTERING = 'entering';\nexport var ENTERED = 'entered';\nexport var EXITING = 'exiting';\n/**\n * The Transition component lets you describe a transition from one component\n * state to another _over time_ with a simple declarative API. Most commonly\n * it's used to animate the mounting and unmounting of a component, but can also\n * be used to describe in-place transition states as well.\n *\n * ---\n *\n * **Note**: `Transition` is a platform-agnostic base component. If you're using\n * transitions in CSS, you'll probably want to use\n * [`CSSTransition`](https://reactcommunity.org/react-transition-group/css-transition)\n * instead. It inherits all the features of `Transition`, but contains\n * additional features necessary to play nice with CSS transitions (hence the\n * name of the component).\n *\n * ---\n *\n * By default the `Transition` component does not alter the behavior of the\n * component it renders, it only tracks \"enter\" and \"exit\" states for the\n * components. It's up to you to give meaning and effect to those states. For\n * example we can add styles to a component when it enters or exits:\n *\n * ```jsx\n * import { Transition } from 'react-transition-group';\n *\n * const duration = 300;\n *\n * const defaultStyle = {\n *   transition: `opacity ${duration}ms ease-in-out`,\n *   opacity: 0,\n * }\n *\n * const transitionStyles = {\n *   entering: { opacity: 1 },\n *   entered:  { opacity: 1 },\n *   exiting:  { opacity: 0 },\n *   exited:  { opacity: 0 },\n * };\n *\n * const Fade = ({ in: inProp }) => (\n *   <Transition in={inProp} timeout={duration}>\n *     {state => (\n *       <div style={{\n *         ...defaultStyle,\n *         ...transitionStyles[state]\n *       }}>\n *         I'm a fade Transition!\n *       </div>\n *     )}\n *   </Transition>\n * );\n * ```\n *\n * There are 4 main states a Transition can be in:\n *  - `'entering'`\n *  - `'entered'`\n *  - `'exiting'`\n *  - `'exited'`\n *\n * Transition state is toggled via the `in` prop. When `true` the component\n * begins the \"Enter\" stage. During this stage, the component will shift from\n * its current transition state, to `'entering'` for the duration of the\n * transition and then to the `'entered'` stage once it's complete. Let's take\n * the following example (we'll use the\n * [useState](https://reactjs.org/docs/hooks-reference.html#usestate) hook):\n *\n * ```jsx\n * function App() {\n *   const [inProp, setInProp] = useState(false);\n *   return (\n *     <div>\n *       <Transition in={inProp} timeout={500}>\n *         {state => (\n *           // ...\n *         )}\n *       </Transition>\n *       <button onClick={() => setInProp(true)}>\n *         Click to Enter\n *       </button>\n *     </div>\n *   );\n * }\n * ```\n *\n * When the button is clicked the component will shift to the `'entering'` state\n * and stay there for 500ms (the value of `timeout`) before it finally switches\n * to `'entered'`.\n *\n * When `in` is `false` the same thing happens except the state moves from\n * `'exiting'` to `'exited'`.\n */\n\nvar Transition = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(Transition, _React$Component);\n\n  function Transition(props, context) {\n    var _this;\n\n    _this = _React$Component.call(this, props, context) || this;\n    var parentGroup = context; // In the context of a TransitionGroup all enters are really appears\n\n    var appear = parentGroup && !parentGroup.isMounting ? props.enter : props.appear;\n    var initialStatus;\n    _this.appearStatus = null;\n\n    if (props.in) {\n      if (appear) {\n        initialStatus = EXITED;\n        _this.appearStatus = ENTERING;\n      } else {\n        initialStatus = ENTERED;\n      }\n    } else {\n      if (props.unmountOnExit || props.mountOnEnter) {\n        initialStatus = UNMOUNTED;\n      } else {\n        initialStatus = EXITED;\n      }\n    }\n\n    _this.state = {\n      status: initialStatus\n    };\n    _this.nextCallback = null;\n    return _this;\n  }\n\n  Transition.getDerivedStateFromProps = function getDerivedStateFromProps(_ref, prevState) {\n    var nextIn = _ref.in;\n\n    if (nextIn && prevState.status === UNMOUNTED) {\n      return {\n        status: EXITED\n      };\n    }\n\n    return null;\n  } // getSnapshotBeforeUpdate(prevProps) {\n  //   let nextStatus = null\n  //   if (prevProps !== this.props) {\n  //     const { status } = this.state\n  //     if (this.props.in) {\n  //       if (status !== ENTERING && status !== ENTERED) {\n  //         nextStatus = ENTERING\n  //       }\n  //     } else {\n  //       if (status === ENTERING || status === ENTERED) {\n  //         nextStatus = EXITING\n  //       }\n  //     }\n  //   }\n  //   return { nextStatus }\n  // }\n  ;\n\n  var _proto = Transition.prototype;\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.updateStatus(true, this.appearStatus);\n  };\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    var nextStatus = null;\n\n    if (prevProps !== this.props) {\n      var status = this.state.status;\n\n      if (this.props.in) {\n        if (status !== ENTERING && status !== ENTERED) {\n          nextStatus = ENTERING;\n        }\n      } else {\n        if (status === ENTERING || status === ENTERED) {\n          nextStatus = EXITING;\n        }\n      }\n    }\n\n    this.updateStatus(false, nextStatus);\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.cancelNextCallback();\n  };\n\n  _proto.getTimeouts = function getTimeouts() {\n    var timeout = this.props.timeout;\n    var exit, enter, appear;\n    exit = enter = appear = timeout;\n\n    if (timeout != null && typeof timeout !== 'number') {\n      exit = timeout.exit;\n      enter = timeout.enter; // TODO: remove fallback for next major\n\n      appear = timeout.appear !== undefined ? timeout.appear : enter;\n    }\n\n    return {\n      exit: exit,\n      enter: enter,\n      appear: appear\n    };\n  };\n\n  _proto.updateStatus = function updateStatus(mounting, nextStatus) {\n    if (mounting === void 0) {\n      mounting = false;\n    }\n\n    if (nextStatus !== null) {\n      // nextStatus will always be ENTERING or EXITING.\n      this.cancelNextCallback();\n\n      if (nextStatus === ENTERING) {\n        if (this.props.unmountOnExit || this.props.mountOnEnter) {\n          var node = this.props.nodeRef ? this.props.nodeRef.current : ReactDOM.findDOMNode(this); // https://github.com/reactjs/react-transition-group/pull/749\n          // With unmountOnExit or mountOnEnter, the enter animation should happen at the transition between `exited` and `entering`.\n          // To make the animation happen,  we have to separate each rendering and avoid being processed as batched.\n\n          if (node) forceReflow(node);\n        }\n\n        this.performEnter(mounting);\n      } else {\n        this.performExit();\n      }\n    } else if (this.props.unmountOnExit && this.state.status === EXITED) {\n      this.setState({\n        status: UNMOUNTED\n      });\n    }\n  };\n\n  _proto.performEnter = function performEnter(mounting) {\n    var _this2 = this;\n\n    var enter = this.props.enter;\n    var appearing = this.context ? this.context.isMounting : mounting;\n\n    var _ref2 = this.props.nodeRef ? [appearing] : [ReactDOM.findDOMNode(this), appearing],\n        maybeNode = _ref2[0],\n        maybeAppearing = _ref2[1];\n\n    var timeouts = this.getTimeouts();\n    var enterTimeout = appearing ? timeouts.appear : timeouts.enter; // no enter animation skip right to ENTERED\n    // if we are mounting and running this it means appear _must_ be set\n\n    if (!mounting && !enter || config.disabled) {\n      this.safeSetState({\n        status: ENTERED\n      }, function () {\n        _this2.props.onEntered(maybeNode);\n      });\n      return;\n    }\n\n    this.props.onEnter(maybeNode, maybeAppearing);\n    this.safeSetState({\n      status: ENTERING\n    }, function () {\n      _this2.props.onEntering(maybeNode, maybeAppearing);\n\n      _this2.onTransitionEnd(enterTimeout, function () {\n        _this2.safeSetState({\n          status: ENTERED\n        }, function () {\n          _this2.props.onEntered(maybeNode, maybeAppearing);\n        });\n      });\n    });\n  };\n\n  _proto.performExit = function performExit() {\n    var _this3 = this;\n\n    var exit = this.props.exit;\n    var timeouts = this.getTimeouts();\n    var maybeNode = this.props.nodeRef ? undefined : ReactDOM.findDOMNode(this); // no exit animation skip right to EXITED\n\n    if (!exit || config.disabled) {\n      this.safeSetState({\n        status: EXITED\n      }, function () {\n        _this3.props.onExited(maybeNode);\n      });\n      return;\n    }\n\n    this.props.onExit(maybeNode);\n    this.safeSetState({\n      status: EXITING\n    }, function () {\n      _this3.props.onExiting(maybeNode);\n\n      _this3.onTransitionEnd(timeouts.exit, function () {\n        _this3.safeSetState({\n          status: EXITED\n        }, function () {\n          _this3.props.onExited(maybeNode);\n        });\n      });\n    });\n  };\n\n  _proto.cancelNextCallback = function cancelNextCallback() {\n    if (this.nextCallback !== null) {\n      this.nextCallback.cancel();\n      this.nextCallback = null;\n    }\n  };\n\n  _proto.safeSetState = function safeSetState(nextState, callback) {\n    // This shouldn't be necessary, but there are weird race conditions with\n    // setState callbacks and unmounting in testing, so always make sure that\n    // we can cancel any pending setState callbacks after we unmount.\n    callback = this.setNextCallback(callback);\n    this.setState(nextState, callback);\n  };\n\n  _proto.setNextCallback = function setNextCallback(callback) {\n    var _this4 = this;\n\n    var active = true;\n\n    this.nextCallback = function (event) {\n      if (active) {\n        active = false;\n        _this4.nextCallback = null;\n        callback(event);\n      }\n    };\n\n    this.nextCallback.cancel = function () {\n      active = false;\n    };\n\n    return this.nextCallback;\n  };\n\n  _proto.onTransitionEnd = function onTransitionEnd(timeout, handler) {\n    this.setNextCallback(handler);\n    var node = this.props.nodeRef ? this.props.nodeRef.current : ReactDOM.findDOMNode(this);\n    var doesNotHaveTimeoutOrListener = timeout == null && !this.props.addEndListener;\n\n    if (!node || doesNotHaveTimeoutOrListener) {\n      setTimeout(this.nextCallback, 0);\n      return;\n    }\n\n    if (this.props.addEndListener) {\n      var _ref3 = this.props.nodeRef ? [this.nextCallback] : [node, this.nextCallback],\n          maybeNode = _ref3[0],\n          maybeNextCallback = _ref3[1];\n\n      this.props.addEndListener(maybeNode, maybeNextCallback);\n    }\n\n    if (timeout != null) {\n      setTimeout(this.nextCallback, timeout);\n    }\n  };\n\n  _proto.render = function render() {\n    var status = this.state.status;\n\n    if (status === UNMOUNTED) {\n      return null;\n    }\n\n    var _this$props = this.props,\n        children = _this$props.children,\n        _in = _this$props.in,\n        _mountOnEnter = _this$props.mountOnEnter,\n        _unmountOnExit = _this$props.unmountOnExit,\n        _appear = _this$props.appear,\n        _enter = _this$props.enter,\n        _exit = _this$props.exit,\n        _timeout = _this$props.timeout,\n        _addEndListener = _this$props.addEndListener,\n        _onEnter = _this$props.onEnter,\n        _onEntering = _this$props.onEntering,\n        _onEntered = _this$props.onEntered,\n        _onExit = _this$props.onExit,\n        _onExiting = _this$props.onExiting,\n        _onExited = _this$props.onExited,\n        _nodeRef = _this$props.nodeRef,\n        childProps = _objectWithoutPropertiesLoose(_this$props, [\"children\", \"in\", \"mountOnEnter\", \"unmountOnExit\", \"appear\", \"enter\", \"exit\", \"timeout\", \"addEndListener\", \"onEnter\", \"onEntering\", \"onEntered\", \"onExit\", \"onExiting\", \"onExited\", \"nodeRef\"]);\n\n    return (\n      /*#__PURE__*/\n      // allows for nested Transitions\n      React.createElement(TransitionGroupContext.Provider, {\n        value: null\n      }, typeof children === 'function' ? children(status, childProps) : React.cloneElement(React.Children.only(children), childProps))\n    );\n  };\n\n  return Transition;\n}(React.Component);\n\nTransition.contextType = TransitionGroupContext;\nTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * A React reference to DOM element that need to transition:\n   * https://stackoverflow.com/a/51127130/4671932\n   *\n   *   - When `nodeRef` prop is used, `node` is not passed to callback functions\n   *      (e.g. `onEnter`) because user already has direct access to the node.\n   *   - When changing `key` prop of `Transition` in a `TransitionGroup` a new\n   *     `nodeRef` need to be provided to `Transition` with changed `key` prop\n   *     (see\n   *     [test/CSSTransition-test.js](https://github.com/reactjs/react-transition-group/blob/13435f897b3ab71f6e19d724f145596f5910581c/test/CSSTransition-test.js#L362-L437)).\n   */\n  nodeRef: PropTypes.shape({\n    current: typeof Element === 'undefined' ? PropTypes.any : function (propValue, key, componentName, location, propFullName, secret) {\n      var value = propValue[key];\n      return PropTypes.instanceOf(value && 'ownerDocument' in value ? value.ownerDocument.defaultView.Element : Element)(propValue, key, componentName, location, propFullName, secret);\n    }\n  }),\n\n  /**\n   * A `function` child can be used instead of a React element. This function is\n   * called with the current transition status (`'entering'`, `'entered'`,\n   * `'exiting'`, `'exited'`), which can be used to apply context\n   * specific props to a component.\n   *\n   * ```jsx\n   * <Transition in={this.state.in} timeout={150}>\n   *   {state => (\n   *     <MyComponent className={`fade fade-${state}`} />\n   *   )}\n   * </Transition>\n   * ```\n   */\n  children: PropTypes.oneOfType([PropTypes.func.isRequired, PropTypes.element.isRequired]).isRequired,\n\n  /**\n   * Show the component; triggers the enter or exit states\n   */\n  in: PropTypes.bool,\n\n  /**\n   * By default the child component is mounted immediately along with\n   * the parent `Transition` component. If you want to \"lazy mount\" the component on the\n   * first `in={true}` you can set `mountOnEnter`. After the first enter transition the component will stay\n   * mounted, even on \"exited\", unless you also specify `unmountOnExit`.\n   */\n  mountOnEnter: PropTypes.bool,\n\n  /**\n   * By default the child component stays mounted after it reaches the `'exited'` state.\n   * Set `unmountOnExit` if you'd prefer to unmount the component after it finishes exiting.\n   */\n  unmountOnExit: PropTypes.bool,\n\n  /**\n   * By default the child component does not perform the enter transition when\n   * it first mounts, regardless of the value of `in`. If you want this\n   * behavior, set both `appear` and `in` to `true`.\n   *\n   * > **Note**: there are no special appear states like `appearing`/`appeared`, this prop\n   * > only adds an additional enter transition. However, in the\n   * > `<CSSTransition>` component that first enter transition does result in\n   * > additional `.appear-*` classes, that way you can choose to style it\n   * > differently.\n   */\n  appear: PropTypes.bool,\n\n  /**\n   * Enable or disable enter transitions.\n   */\n  enter: PropTypes.bool,\n\n  /**\n   * Enable or disable exit transitions.\n   */\n  exit: PropTypes.bool,\n\n  /**\n   * The duration of the transition, in milliseconds.\n   * Required unless `addEndListener` is provided.\n   *\n   * You may specify a single timeout for all transitions:\n   *\n   * ```jsx\n   * timeout={500}\n   * ```\n   *\n   * or individually:\n   *\n   * ```jsx\n   * timeout={{\n   *  appear: 500,\n   *  enter: 300,\n   *  exit: 500,\n   * }}\n   * ```\n   *\n   * - `appear` defaults to the value of `enter`\n   * - `enter` defaults to `0`\n   * - `exit` defaults to `0`\n   *\n   * @type {number | { enter?: number, exit?: number, appear?: number }}\n   */\n  timeout: function timeout(props) {\n    var pt = timeoutsShape;\n    if (!props.addEndListener) pt = pt.isRequired;\n\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return pt.apply(void 0, [props].concat(args));\n  },\n\n  /**\n   * Add a custom transition end trigger. Called with the transitioning\n   * DOM node and a `done` callback. Allows for more fine grained transition end\n   * logic. Timeouts are still used as a fallback if provided.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * ```jsx\n   * addEndListener={(node, done) => {\n   *   // use the css transitionend event to mark the finish of a transition\n   *   node.addEventListener('transitionend', done, false);\n   * }}\n   * ```\n   */\n  addEndListener: PropTypes.func,\n\n  /**\n   * Callback fired before the \"entering\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\n   */\n  onEnter: PropTypes.func,\n\n  /**\n   * Callback fired after the \"entering\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntering: PropTypes.func,\n\n  /**\n   * Callback fired after the \"entered\" status is applied. An extra parameter\n   * `isAppearing` is supplied to indicate if the enter stage is occurring on the initial mount\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool) -> void\n   */\n  onEntered: PropTypes.func,\n\n  /**\n   * Callback fired before the \"exiting\" status is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExit: PropTypes.func,\n\n  /**\n   * Callback fired after the \"exiting\" status is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExiting: PropTypes.func,\n\n  /**\n   * Callback fired after the \"exited\" status is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement) -> void\n   */\n  onExited: PropTypes.func\n} : {}; // Name the function so it is clearer in the documentation\n\nfunction noop() {}\n\nTransition.defaultProps = {\n  in: false,\n  mountOnEnter: false,\n  unmountOnExit: false,\n  appear: false,\n  enter: true,\n  exit: true,\n  onEnter: noop,\n  onEntering: noop,\n  onEntered: noop,\n  onExit: noop,\n  onExiting: noop,\n  onExited: noop\n};\nTransition.UNMOUNTED = UNMOUNTED;\nTransition.EXITED = EXITED;\nTransition.ENTERING = ENTERING;\nTransition.ENTERED = ENTERED;\nTransition.EXITING = EXITING;\nexport default Transition;", "export default {\n  disabled: false\n};", "import PropTypes from 'prop-types';\nexport var timeoutsShape = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n  enter: PropTypes.number,\n  exit: PropTypes.number,\n  appear: PropTypes.number\n}).isRequired]) : null;\nexport var classNamesShape = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.string, PropTypes.shape({\n  enter: PropTypes.string,\n  exit: PropTypes.string,\n  active: PropTypes.string\n}), PropTypes.shape({\n  enter: PropTypes.string,\n  enterDone: PropTypes.string,\n  enterActive: PropTypes.string,\n  exit: PropTypes.string,\n  exitDone: PropTypes.string,\n  exitActive: PropTypes.string\n})]) : null;", "import React from 'react';\nexport default React.createContext(null);", "export var forceReflow = function forceReflow(node) {\n  return node.scrollTop;\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport addOneClass from 'dom-helpers/addClass';\nimport removeOneClass from 'dom-helpers/removeClass';\nimport React from 'react';\nimport Transition from './Transition';\nimport { classNamesShape } from './utils/PropTypes';\nimport { forceReflow } from './utils/reflow';\n\nvar _addClass = function addClass(node, classes) {\n  return node && classes && classes.split(' ').forEach(function (c) {\n    return addOneClass(node, c);\n  });\n};\n\nvar removeClass = function removeClass(node, classes) {\n  return node && classes && classes.split(' ').forEach(function (c) {\n    return removeOneClass(node, c);\n  });\n};\n/**\n * A transition component inspired by the excellent\n * [ng-animate](https://docs.angularjs.org/api/ngAnimate) library, you should\n * use it if you're using CSS transitions or animations. It's built upon the\n * [`Transition`](https://reactcommunity.org/react-transition-group/transition)\n * component, so it inherits all of its props.\n *\n * `CSSTransition` applies a pair of class names during the `appear`, `enter`,\n * and `exit` states of the transition. The first class is applied and then a\n * second `*-active` class in order to activate the CSS transition. After the\n * transition, matching `*-done` class names are applied to persist the\n * transition state.\n *\n * ```jsx\n * function App() {\n *   const [inProp, setInProp] = useState(false);\n *   return (\n *     <div>\n *       <CSSTransition in={inProp} timeout={200} classNames=\"my-node\">\n *         <div>\n *           {\"I'll receive my-node-* classes\"}\n *         </div>\n *       </CSSTransition>\n *       <button type=\"button\" onClick={() => setInProp(true)}>\n *         Click to Enter\n *       </button>\n *     </div>\n *   );\n * }\n * ```\n *\n * When the `in` prop is set to `true`, the child component will first receive\n * the class `example-enter`, then the `example-enter-active` will be added in\n * the next tick. `CSSTransition` [forces a\n * reflow](https://github.com/reactjs/react-transition-group/blob/5007303e729a74be66a21c3e2205e4916821524b/src/CSSTransition.js#L208-L215)\n * between before adding the `example-enter-active`. This is an important trick\n * because it allows us to transition between `example-enter` and\n * `example-enter-active` even though they were added immediately one after\n * another. Most notably, this is what makes it possible for us to animate\n * _appearance_.\n *\n * ```css\n * .my-node-enter {\n *   opacity: 0;\n * }\n * .my-node-enter-active {\n *   opacity: 1;\n *   transition: opacity 200ms;\n * }\n * .my-node-exit {\n *   opacity: 1;\n * }\n * .my-node-exit-active {\n *   opacity: 0;\n *   transition: opacity 200ms;\n * }\n * ```\n *\n * `*-active` classes represent which styles you want to animate **to**, so it's\n * important to add `transition` declaration only to them, otherwise transitions\n * might not behave as intended! This might not be obvious when the transitions\n * are symmetrical, i.e. when `*-enter-active` is the same as `*-exit`, like in\n * the example above (minus `transition`), but it becomes apparent in more\n * complex transitions.\n *\n * **Note**: If you're using the\n * [`appear`](http://reactcommunity.org/react-transition-group/transition#Transition-prop-appear)\n * prop, make sure to define styles for `.appear-*` classes as well.\n */\n\n\nvar CSSTransition = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(CSSTransition, _React$Component);\n\n  function CSSTransition() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.appliedClasses = {\n      appear: {},\n      enter: {},\n      exit: {}\n    };\n\n    _this.onEnter = function (maybeNode, maybeAppearing) {\n      var _this$resolveArgument = _this.resolveArguments(maybeNode, maybeAppearing),\n          node = _this$resolveArgument[0],\n          appearing = _this$resolveArgument[1];\n\n      _this.removeClasses(node, 'exit');\n\n      _this.addClass(node, appearing ? 'appear' : 'enter', 'base');\n\n      if (_this.props.onEnter) {\n        _this.props.onEnter(maybeNode, maybeAppearing);\n      }\n    };\n\n    _this.onEntering = function (maybeNode, maybeAppearing) {\n      var _this$resolveArgument2 = _this.resolveArguments(maybeNode, maybeAppearing),\n          node = _this$resolveArgument2[0],\n          appearing = _this$resolveArgument2[1];\n\n      var type = appearing ? 'appear' : 'enter';\n\n      _this.addClass(node, type, 'active');\n\n      if (_this.props.onEntering) {\n        _this.props.onEntering(maybeNode, maybeAppearing);\n      }\n    };\n\n    _this.onEntered = function (maybeNode, maybeAppearing) {\n      var _this$resolveArgument3 = _this.resolveArguments(maybeNode, maybeAppearing),\n          node = _this$resolveArgument3[0],\n          appearing = _this$resolveArgument3[1];\n\n      var type = appearing ? 'appear' : 'enter';\n\n      _this.removeClasses(node, type);\n\n      _this.addClass(node, type, 'done');\n\n      if (_this.props.onEntered) {\n        _this.props.onEntered(maybeNode, maybeAppearing);\n      }\n    };\n\n    _this.onExit = function (maybeNode) {\n      var _this$resolveArgument4 = _this.resolveArguments(maybeNode),\n          node = _this$resolveArgument4[0];\n\n      _this.removeClasses(node, 'appear');\n\n      _this.removeClasses(node, 'enter');\n\n      _this.addClass(node, 'exit', 'base');\n\n      if (_this.props.onExit) {\n        _this.props.onExit(maybeNode);\n      }\n    };\n\n    _this.onExiting = function (maybeNode) {\n      var _this$resolveArgument5 = _this.resolveArguments(maybeNode),\n          node = _this$resolveArgument5[0];\n\n      _this.addClass(node, 'exit', 'active');\n\n      if (_this.props.onExiting) {\n        _this.props.onExiting(maybeNode);\n      }\n    };\n\n    _this.onExited = function (maybeNode) {\n      var _this$resolveArgument6 = _this.resolveArguments(maybeNode),\n          node = _this$resolveArgument6[0];\n\n      _this.removeClasses(node, 'exit');\n\n      _this.addClass(node, 'exit', 'done');\n\n      if (_this.props.onExited) {\n        _this.props.onExited(maybeNode);\n      }\n    };\n\n    _this.resolveArguments = function (maybeNode, maybeAppearing) {\n      return _this.props.nodeRef ? [_this.props.nodeRef.current, maybeNode] // here `maybeNode` is actually `appearing`\n      : [maybeNode, maybeAppearing];\n    };\n\n    _this.getClassNames = function (type) {\n      var classNames = _this.props.classNames;\n      var isStringClassNames = typeof classNames === 'string';\n      var prefix = isStringClassNames && classNames ? classNames + \"-\" : '';\n      var baseClassName = isStringClassNames ? \"\" + prefix + type : classNames[type];\n      var activeClassName = isStringClassNames ? baseClassName + \"-active\" : classNames[type + \"Active\"];\n      var doneClassName = isStringClassNames ? baseClassName + \"-done\" : classNames[type + \"Done\"];\n      return {\n        baseClassName: baseClassName,\n        activeClassName: activeClassName,\n        doneClassName: doneClassName\n      };\n    };\n\n    return _this;\n  }\n\n  var _proto = CSSTransition.prototype;\n\n  _proto.addClass = function addClass(node, type, phase) {\n    var className = this.getClassNames(type)[phase + \"ClassName\"];\n\n    var _this$getClassNames = this.getClassNames('enter'),\n        doneClassName = _this$getClassNames.doneClassName;\n\n    if (type === 'appear' && phase === 'done' && doneClassName) {\n      className += \" \" + doneClassName;\n    } // This is to force a repaint,\n    // which is necessary in order to transition styles when adding a class name.\n\n\n    if (phase === 'active') {\n      if (node) forceReflow(node);\n    }\n\n    if (className) {\n      this.appliedClasses[type][phase] = className;\n\n      _addClass(node, className);\n    }\n  };\n\n  _proto.removeClasses = function removeClasses(node, type) {\n    var _this$appliedClasses$ = this.appliedClasses[type],\n        baseClassName = _this$appliedClasses$.base,\n        activeClassName = _this$appliedClasses$.active,\n        doneClassName = _this$appliedClasses$.done;\n    this.appliedClasses[type] = {};\n\n    if (baseClassName) {\n      removeClass(node, baseClassName);\n    }\n\n    if (activeClassName) {\n      removeClass(node, activeClassName);\n    }\n\n    if (doneClassName) {\n      removeClass(node, doneClassName);\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        _ = _this$props.classNames,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"classNames\"]);\n\n    return /*#__PURE__*/React.createElement(Transition, _extends({}, props, {\n      onEnter: this.onEnter,\n      onEntered: this.onEntered,\n      onEntering: this.onEntering,\n      onExit: this.onExit,\n      onExiting: this.onExiting,\n      onExited: this.onExited\n    }));\n  };\n\n  return CSSTransition;\n}(React.Component);\n\nCSSTransition.defaultProps = {\n  classNames: ''\n};\nCSSTransition.propTypes = process.env.NODE_ENV !== \"production\" ? _extends({}, Transition.propTypes, {\n  /**\n   * The animation classNames applied to the component as it appears, enters,\n   * exits or has finished the transition. A single name can be provided, which\n   * will be suffixed for each stage, e.g. `classNames=\"fade\"` applies:\n   *\n   * - `fade-appear`, `fade-appear-active`, `fade-appear-done`\n   * - `fade-enter`, `fade-enter-active`, `fade-enter-done`\n   * - `fade-exit`, `fade-exit-active`, `fade-exit-done`\n   *\n   * A few details to note about how these classes are applied:\n   *\n   * 1. They are _joined_ with the ones that are already defined on the child\n   *    component, so if you want to add some base styles, you can use\n   *    `className` without worrying that it will be overridden.\n   *\n   * 2. If the transition component mounts with `in={false}`, no classes are\n   *    applied yet. You might be expecting `*-exit-done`, but if you think\n   *    about it, a component cannot finish exiting if it hasn't entered yet.\n   *\n   * 2. `fade-appear-done` and `fade-enter-done` will _both_ be applied. This\n   *    allows you to define different behavior for when appearing is done and\n   *    when regular entering is done, using selectors like\n   *    `.fade-enter-done:not(.fade-appear-done)`. For example, you could apply\n   *    an epic entrance animation when element first appears in the DOM using\n   *    [Animate.css](https://daneden.github.io/animate.css/). Otherwise you can\n   *    simply use `fade-enter-done` for defining both cases.\n   *\n   * Each individual classNames can also be specified independently like:\n   *\n   * ```js\n   * classNames={{\n   *  appear: 'my-appear',\n   *  appearActive: 'my-active-appear',\n   *  appearDone: 'my-done-appear',\n   *  enter: 'my-enter',\n   *  enterActive: 'my-active-enter',\n   *  enterDone: 'my-done-enter',\n   *  exit: 'my-exit',\n   *  exitActive: 'my-active-exit',\n   *  exitDone: 'my-done-exit',\n   * }}\n   * ```\n   *\n   * If you want to set these classes using CSS Modules:\n   *\n   * ```js\n   * import styles from './styles.css';\n   * ```\n   *\n   * you might want to use camelCase in your CSS file, that way could simply\n   * spread them instead of listing them one by one:\n   *\n   * ```js\n   * classNames={{ ...styles }}\n   * ```\n   *\n   * @type {string | {\n   *  appear?: string,\n   *  appearActive?: string,\n   *  appearDone?: string,\n   *  enter?: string,\n   *  enterActive?: string,\n   *  enterDone?: string,\n   *  exit?: string,\n   *  exitActive?: string,\n   *  exitDone?: string,\n   * }}\n   */\n  classNames: classNamesShape,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter' or 'appear' class is\n   * applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEnter: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter-active' or\n   * 'appear-active' class is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntering: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter' or\n   * 'appear' classes are **removed** and the `done` class is added to the DOM node.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntered: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit' class is\n   * applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExit: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit-active' is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExiting: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit' classes\n   * are **removed** and the `exit-done` class is added to the DOM node.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExited: PropTypes.func\n}) : {};\nexport default CSSTransition;", "/**\n * Checks if a given element has a CSS class.\n * \n * @param element the element\n * @param className the CSS class name\n */\nexport default function hasClass(element, className) {\n  if (element.classList) return !!className && element.classList.contains(className);\n  return (\" \" + (element.className.baseVal || element.className) + \" \").indexOf(\" \" + className + \" \") !== -1;\n}", "import hasClass from './hasClass';\n/**\n * Adds a CSS class to a given element.\n * \n * @param element the element\n * @param className the CSS class name\n */\n\nexport default function addClass(element, className) {\n  if (element.classList) element.classList.add(className);else if (!hasClass(element, className)) if (typeof element.className === 'string') element.className = element.className + \" \" + className;else element.setAttribute('class', (element.className && element.className.baseVal || '') + \" \" + className);\n}", "function replaceClassName(origClass, classToRemove) {\n  return origClass.replace(new RegExp(\"(^|\\\\s)\" + classToRemove + \"(?:\\\\s|$)\", 'g'), '$1').replace(/\\s+/g, ' ').replace(/^\\s*|\\s*$/g, '');\n}\n/**\n * Removes a CSS class from a given element.\n * \n * @param element the element\n * @param className the CSS class name\n */\n\n\nexport default function removeClass(element, className) {\n  if (element.classList) {\n    element.classList.remove(className);\n  } else if (typeof element.className === 'string') {\n    element.className = replaceClassName(element.className, className);\n  } else {\n    element.setAttribute('class', replaceClassName(element.className && element.className.baseVal || '', className));\n  }\n}", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport TransitionGroup from './TransitionGroup';\n/**\n * The `<ReplaceTransition>` component is a specialized `Transition` component\n * that animates between two children.\n *\n * ```jsx\n * <ReplaceTransition in>\n *   <Fade><div>I appear first</div></Fade>\n *   <Fade><div>I replace the above</div></Fade>\n * </ReplaceTransition>\n * ```\n */\n\nvar ReplaceTransition = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(ReplaceTransition, _React$Component);\n\n  function ReplaceTransition() {\n    var _this;\n\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(_args)) || this;\n\n    _this.handleEnter = function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      return _this.handleLifecycle('onEnter', 0, args);\n    };\n\n    _this.handleEntering = function () {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n\n      return _this.handleLifecycle('onEntering', 0, args);\n    };\n\n    _this.handleEntered = function () {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n\n      return _this.handleLifecycle('onEntered', 0, args);\n    };\n\n    _this.handleExit = function () {\n      for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n        args[_key5] = arguments[_key5];\n      }\n\n      return _this.handleLifecycle('onExit', 1, args);\n    };\n\n    _this.handleExiting = function () {\n      for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n        args[_key6] = arguments[_key6];\n      }\n\n      return _this.handleLifecycle('onExiting', 1, args);\n    };\n\n    _this.handleExited = function () {\n      for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n        args[_key7] = arguments[_key7];\n      }\n\n      return _this.handleLifecycle('onExited', 1, args);\n    };\n\n    return _this;\n  }\n\n  var _proto = ReplaceTransition.prototype;\n\n  _proto.handleLifecycle = function handleLifecycle(handler, idx, originalArgs) {\n    var _child$props;\n\n    var children = this.props.children;\n    var child = React.Children.toArray(children)[idx];\n    if (child.props[handler]) (_child$props = child.props)[handler].apply(_child$props, originalArgs);\n\n    if (this.props[handler]) {\n      var maybeNode = child.props.nodeRef ? undefined : ReactDOM.findDOMNode(this);\n      this.props[handler](maybeNode);\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        children = _this$props.children,\n        inProp = _this$props.in,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"children\", \"in\"]);\n\n    var _React$Children$toArr = React.Children.toArray(children),\n        first = _React$Children$toArr[0],\n        second = _React$Children$toArr[1];\n\n    delete props.onEnter;\n    delete props.onEntering;\n    delete props.onEntered;\n    delete props.onExit;\n    delete props.onExiting;\n    delete props.onExited;\n    return /*#__PURE__*/React.createElement(TransitionGroup, props, inProp ? React.cloneElement(first, {\n      key: 'first',\n      onEnter: this.handleEnter,\n      onEntering: this.handleEntering,\n      onEntered: this.handleEntered\n    }) : React.cloneElement(second, {\n      key: 'second',\n      onEnter: this.handleExit,\n      onEntering: this.handleExiting,\n      onEntered: this.handleExited\n    }));\n  };\n\n  return ReplaceTransition;\n}(React.Component);\n\nReplaceTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  in: PropTypes.bool.isRequired,\n  children: function children(props, propName) {\n    if (React.Children.count(props[propName]) !== 2) return new Error(\"\\\"\" + propName + \"\\\" must be exactly two transition components.\");\n    return null;\n  }\n} : {};\nexport default ReplaceTransition;", "function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport TransitionGroupContext from './TransitionGroupContext';\nimport { getChildMapping, getInitialChildMapping, getNextChildMapping } from './utils/ChildMapping';\n\nvar values = Object.values || function (obj) {\n  return Object.keys(obj).map(function (k) {\n    return obj[k];\n  });\n};\n\nvar defaultProps = {\n  component: 'div',\n  childFactory: function childFactory(child) {\n    return child;\n  }\n};\n/**\n * The `<TransitionGroup>` component manages a set of transition components\n * (`<Transition>` and `<CSSTransition>`) in a list. Like with the transition\n * components, `<TransitionGroup>` is a state machine for managing the mounting\n * and unmounting of components over time.\n *\n * Consider the example below. As items are removed or added to the TodoList the\n * `in` prop is toggled automatically by the `<TransitionGroup>`.\n *\n * Note that `<TransitionGroup>`  does not define any animation behavior!\n * Exactly _how_ a list item animates is up to the individual transition\n * component. This means you can mix and match animations across different list\n * items.\n */\n\nvar TransitionGroup = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(TransitionGroup, _React$Component);\n\n  function TransitionGroup(props, context) {\n    var _this;\n\n    _this = _React$Component.call(this, props, context) || this;\n\n    var handleExited = _this.handleExited.bind(_assertThisInitialized(_this)); // Initial children should all be entering, dependent on appear\n\n\n    _this.state = {\n      contextValue: {\n        isMounting: true\n      },\n      handleExited: handleExited,\n      firstRender: true\n    };\n    return _this;\n  }\n\n  var _proto = TransitionGroup.prototype;\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.mounted = true;\n    this.setState({\n      contextValue: {\n        isMounting: false\n      }\n    });\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.mounted = false;\n  };\n\n  TransitionGroup.getDerivedStateFromProps = function getDerivedStateFromProps(nextProps, _ref) {\n    var prevChildMapping = _ref.children,\n        handleExited = _ref.handleExited,\n        firstRender = _ref.firstRender;\n    return {\n      children: firstRender ? getInitialChildMapping(nextProps, handleExited) : getNextChildMapping(nextProps, prevChildMapping, handleExited),\n      firstRender: false\n    };\n  } // node is `undefined` when user provided `nodeRef` prop\n  ;\n\n  _proto.handleExited = function handleExited(child, node) {\n    var currentChildMapping = getChildMapping(this.props.children);\n    if (child.key in currentChildMapping) return;\n\n    if (child.props.onExited) {\n      child.props.onExited(node);\n    }\n\n    if (this.mounted) {\n      this.setState(function (state) {\n        var children = _extends({}, state.children);\n\n        delete children[child.key];\n        return {\n          children: children\n        };\n      });\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        Component = _this$props.component,\n        childFactory = _this$props.childFactory,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"childFactory\"]);\n\n    var contextValue = this.state.contextValue;\n    var children = values(this.state.children).map(childFactory);\n    delete props.appear;\n    delete props.enter;\n    delete props.exit;\n\n    if (Component === null) {\n      return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n        value: contextValue\n      }, children);\n    }\n\n    return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n      value: contextValue\n    }, /*#__PURE__*/React.createElement(Component, props, children));\n  };\n\n  return TransitionGroup;\n}(React.Component);\n\nTransitionGroup.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * `<TransitionGroup>` renders a `<div>` by default. You can change this\n   * behavior by providing a `component` prop.\n   * If you use React v16+ and would like to avoid a wrapping `<div>` element\n   * you can pass in `component={null}`. This is useful if the wrapping div\n   * borks your css styles.\n   */\n  component: PropTypes.any,\n\n  /**\n   * A set of `<Transition>` components, that are toggled `in` and out as they\n   * leave. the `<TransitionGroup>` will inject specific transition props, so\n   * remember to spread them through if you are wrapping the `<Transition>` as\n   * with our `<Fade>` example.\n   *\n   * While this component is meant for multiple `Transition` or `CSSTransition`\n   * children, sometimes you may want to have a single transition child with\n   * content that you want to be transitioned out and in when you change it\n   * (e.g. routes, images etc.) In that case you can change the `key` prop of\n   * the transition child as you change its content, this will cause\n   * `TransitionGroup` to transition the child out and back in.\n   */\n  children: PropTypes.node,\n\n  /**\n   * A convenience prop that enables or disables appear animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  appear: PropTypes.bool,\n\n  /**\n   * A convenience prop that enables or disables enter animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  enter: PropTypes.bool,\n\n  /**\n   * A convenience prop that enables or disables exit animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  exit: PropTypes.bool,\n\n  /**\n   * You may need to apply reactive updates to a child as it is exiting.\n   * This is generally done by using `cloneElement` however in the case of an exiting\n   * child the element has already been removed and not accessible to the consumer.\n   *\n   * If you do need to update a child as it leaves you can provide a `childFactory`\n   * to wrap every child, even the ones that are leaving.\n   *\n   * @type Function(child: ReactElement) -> ReactElement\n   */\n  childFactory: PropTypes.func\n} : {};\nTransitionGroup.defaultProps = defaultProps;\nexport default TransitionGroup;", "import { Children, cloneElement, isValidElement } from 'react';\n/**\n * Given `this.props.children`, return an object mapping key to child.\n *\n * @param {*} children `this.props.children`\n * @return {object} Mapping of key to child\n */\n\nexport function getChildMapping(children, mapFn) {\n  var mapper = function mapper(child) {\n    return mapFn && isValidElement(child) ? mapFn(child) : child;\n  };\n\n  var result = Object.create(null);\n  if (children) Children.map(children, function (c) {\n    return c;\n  }).forEach(function (child) {\n    // run the map function here instead so that the key is the computed one\n    result[child.key] = mapper(child);\n  });\n  return result;\n}\n/**\n * When you're adding or removing children some may be added or removed in the\n * same render pass. We want to show *both* since we want to simultaneously\n * animate elements in and out. This function takes a previous set of keys\n * and a new set of keys and merges them with its best guess of the correct\n * ordering. In the future we may expose some of the utilities in\n * ReactMultiChild to make this easy, but for now React itself does not\n * directly have this concept of the union of prevChildren and nextChildren\n * so we implement it here.\n *\n * @param {object} prev prev children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @param {object} next next children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @return {object} a key set that contains all keys in `prev` and all keys\n * in `next` in a reasonable order.\n */\n\nexport function mergeChildMappings(prev, next) {\n  prev = prev || {};\n  next = next || {};\n\n  function getValueForKey(key) {\n    return key in next ? next[key] : prev[key];\n  } // For each key of `next`, the list of keys to insert before that key in\n  // the combined list\n\n\n  var nextKeysPending = Object.create(null);\n  var pendingKeys = [];\n\n  for (var prevKey in prev) {\n    if (prevKey in next) {\n      if (pendingKeys.length) {\n        nextKeysPending[prevKey] = pendingKeys;\n        pendingKeys = [];\n      }\n    } else {\n      pendingKeys.push(prevKey);\n    }\n  }\n\n  var i;\n  var childMapping = {};\n\n  for (var nextKey in next) {\n    if (nextKeysPending[nextKey]) {\n      for (i = 0; i < nextKeysPending[nextKey].length; i++) {\n        var pendingNextKey = nextKeysPending[nextKey][i];\n        childMapping[nextKeysPending[nextKey][i]] = getValueForKey(pendingNextKey);\n      }\n    }\n\n    childMapping[nextKey] = getValueForKey(nextKey);\n  } // Finally, add the keys which didn't appear before any key in `next`\n\n\n  for (i = 0; i < pendingKeys.length; i++) {\n    childMapping[pendingKeys[i]] = getValueForKey(pendingKeys[i]);\n  }\n\n  return childMapping;\n}\n\nfunction getProp(child, prop, props) {\n  return props[prop] != null ? props[prop] : child.props[prop];\n}\n\nexport function getInitialChildMapping(props, onExited) {\n  return getChildMapping(props.children, function (child) {\n    return cloneElement(child, {\n      onExited: onExited.bind(null, child),\n      in: true,\n      appear: getProp(child, 'appear', props),\n      enter: getProp(child, 'enter', props),\n      exit: getProp(child, 'exit', props)\n    });\n  });\n}\nexport function getNextChildMapping(nextProps, prevChildMapping, onExited) {\n  var nextChildMapping = getChildMapping(nextProps.children);\n  var children = mergeChildMappings(prevChildMapping, nextChildMapping);\n  Object.keys(children).forEach(function (key) {\n    var child = children[key];\n    if (!isValidElement(child)) return;\n    var hasPrev = (key in prevChildMapping);\n    var hasNext = (key in nextChildMapping);\n    var prevChild = prevChildMapping[key];\n    var isLeaving = isValidElement(prevChild) && !prevChild.props.in; // item is new (entering)\n\n    if (hasNext && (!hasPrev || isLeaving)) {\n      // console.log('entering', key)\n      children[key] = cloneElement(child, {\n        onExited: onExited.bind(null, child),\n        in: true,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    } else if (!hasNext && hasPrev && !isLeaving) {\n      // item is old (exiting)\n      // console.log('leaving', key)\n      children[key] = cloneElement(child, {\n        in: false\n      });\n    } else if (hasNext && hasPrev && isValidElement(prevChild)) {\n      // item hasn't changed transition states\n      // copy over the last transition props;\n      // console.log('unchanged', key)\n      children[key] = cloneElement(child, {\n        onExited: onExited.bind(null, child),\n        in: prevChild.props.in,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    }\n  });\n  return children;\n}", "import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\n\nvar _leaveRenders, _enterRenders;\n\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport { ENTERED, ENTERING, EXITING } from './Transition';\nimport TransitionGroupContext from './TransitionGroupContext';\n\nfunction areChildrenDifferent(oldChildren, newChildren) {\n  if (oldChildren === newChildren) return false;\n\n  if (React.isValidElement(oldChildren) && React.isValidElement(newChildren) && oldChildren.key != null && oldChildren.key === newChildren.key) {\n    return false;\n  }\n\n  return true;\n}\n/**\n * Enum of modes for SwitchTransition component\n * @enum { string }\n */\n\n\nexport var modes = {\n  out: 'out-in',\n  in: 'in-out'\n};\n\nvar callHook = function callHook(element, name, cb) {\n  return function () {\n    var _element$props;\n\n    element.props[name] && (_element$props = element.props)[name].apply(_element$props, arguments);\n    cb();\n  };\n};\n\nvar leaveRenders = (_leaveRenders = {}, _leaveRenders[modes.out] = function (_ref) {\n  var current = _ref.current,\n      changeState = _ref.changeState;\n  return React.cloneElement(current, {\n    in: false,\n    onExited: callHook(current, 'onExited', function () {\n      changeState(ENTERING, null);\n    })\n  });\n}, _leaveRenders[modes.in] = function (_ref2) {\n  var current = _ref2.current,\n      changeState = _ref2.changeState,\n      children = _ref2.children;\n  return [current, React.cloneElement(children, {\n    in: true,\n    onEntered: callHook(children, 'onEntered', function () {\n      changeState(ENTERING);\n    })\n  })];\n}, _leaveRenders);\nvar enterRenders = (_enterRenders = {}, _enterRenders[modes.out] = function (_ref3) {\n  var children = _ref3.children,\n      changeState = _ref3.changeState;\n  return React.cloneElement(children, {\n    in: true,\n    onEntered: callHook(children, 'onEntered', function () {\n      changeState(ENTERED, React.cloneElement(children, {\n        in: true\n      }));\n    })\n  });\n}, _enterRenders[modes.in] = function (_ref4) {\n  var current = _ref4.current,\n      children = _ref4.children,\n      changeState = _ref4.changeState;\n  return [React.cloneElement(current, {\n    in: false,\n    onExited: callHook(current, 'onExited', function () {\n      changeState(ENTERED, React.cloneElement(children, {\n        in: true\n      }));\n    })\n  }), React.cloneElement(children, {\n    in: true\n  })];\n}, _enterRenders);\n/**\n * A transition component inspired by the [vue transition modes](https://vuejs.org/v2/guide/transitions.html#Transition-Modes).\n * You can use it when you want to control the render between state transitions.\n * Based on the selected mode and the child's key which is the `Transition` or `CSSTransition` component, the `SwitchTransition` makes a consistent transition between them.\n *\n * If the `out-in` mode is selected, the `SwitchTransition` waits until the old child leaves and then inserts a new child.\n * If the `in-out` mode is selected, the `SwitchTransition` inserts a new child first, waits for the new child to enter and then removes the old child.\n *\n * **Note**: If you want the animation to happen simultaneously\n * (that is, to have the old child removed and a new child inserted **at the same time**),\n * you should use\n * [`TransitionGroup`](https://reactcommunity.org/react-transition-group/transition-group)\n * instead.\n *\n * ```jsx\n * function App() {\n *  const [state, setState] = useState(false);\n *  return (\n *    <SwitchTransition>\n *      <CSSTransition\n *        key={state ? \"Goodbye, world!\" : \"Hello, world!\"}\n *        addEndListener={(node, done) => node.addEventListener(\"transitionend\", done, false)}\n *        classNames='fade'\n *      >\n *        <button onClick={() => setState(state => !state)}>\n *          {state ? \"Goodbye, world!\" : \"Hello, world!\"}\n *        </button>\n *      </CSSTransition>\n *    </SwitchTransition>\n *  );\n * }\n * ```\n *\n * ```css\n * .fade-enter{\n *    opacity: 0;\n * }\n * .fade-exit{\n *    opacity: 1;\n * }\n * .fade-enter-active{\n *    opacity: 1;\n * }\n * .fade-exit-active{\n *    opacity: 0;\n * }\n * .fade-enter-active,\n * .fade-exit-active{\n *    transition: opacity 500ms;\n * }\n * ```\n */\n\nvar SwitchTransition = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(SwitchTransition, _React$Component);\n\n  function SwitchTransition() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.state = {\n      status: ENTERED,\n      current: null\n    };\n    _this.appeared = false;\n\n    _this.changeState = function (status, current) {\n      if (current === void 0) {\n        current = _this.state.current;\n      }\n\n      _this.setState({\n        status: status,\n        current: current\n      });\n    };\n\n    return _this;\n  }\n\n  var _proto = SwitchTransition.prototype;\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.appeared = true;\n  };\n\n  SwitchTransition.getDerivedStateFromProps = function getDerivedStateFromProps(props, state) {\n    if (props.children == null) {\n      return {\n        current: null\n      };\n    }\n\n    if (state.status === ENTERING && props.mode === modes.in) {\n      return {\n        status: ENTERING\n      };\n    }\n\n    if (state.current && areChildrenDifferent(state.current, props.children)) {\n      return {\n        status: EXITING\n      };\n    }\n\n    return {\n      current: React.cloneElement(props.children, {\n        in: true\n      })\n    };\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        children = _this$props.children,\n        mode = _this$props.mode,\n        _this$state = this.state,\n        status = _this$state.status,\n        current = _this$state.current;\n    var data = {\n      children: children,\n      current: current,\n      changeState: this.changeState,\n      status: status\n    };\n    var component;\n\n    switch (status) {\n      case ENTERING:\n        component = enterRenders[mode](data);\n        break;\n\n      case EXITING:\n        component = leaveRenders[mode](data);\n        break;\n\n      case ENTERED:\n        component = current;\n    }\n\n    return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n      value: {\n        isMounting: !this.appeared\n      }\n    }, component);\n  };\n\n  return SwitchTransition;\n}(React.Component);\n\nSwitchTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * Transition modes.\n   * `out-in`: Current element transitions out first, then when complete, the new element transitions in.\n   * `in-out`: New element transitions in first, then when complete, the current element transitions out.\n   *\n   * @type {'out-in'|'in-out'}\n   */\n  mode: PropTypes.oneOf([modes.in, modes.out]),\n\n  /**\n   * Any `Transition` or `CSSTransition` component.\n   */\n  children: PropTypes.oneOfType([PropTypes.element.isRequired])\n} : {};\nSwitchTransition.defaultProps = {\n  mode: modes.out\n};\nexport default SwitchTransition;", "'use client';\n\nimport * as React from 'react';\nconst UNINITIALIZED = {};\n\n/**\n * A React.useRef() that is initialized lazily with a function. Note that it accepts an optional\n * initialization argument, so the initialization function doesn't need to be an inline closure.\n *\n * @usage\n *   const ref = useLazyRef(sortColumns, columns)\n */\nexport default function useLazyRef(init, initArg) {\n  const ref = React.useRef(UNINITIALIZED);\n  if (ref.current === UNINITIALIZED) {\n    ref.current = init(initArg);\n  }\n  return ref;\n}", "'use client';\n\nimport * as React from 'react';\nconst EMPTY = [];\n\n/**\n * A React.useEffect equivalent that runs once, when the component is mounted.\n */\nexport default function useOnMount(fn) {\n  // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- no need to put `fn` in the dependency array\n  /* eslint-disable react-hooks/exhaustive-deps */\n  React.useEffect(fn, EMPTY);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}", "'use client';\n\nimport useLazyRef from \"../useLazyRef/useLazyRef.js\";\nimport useOnMount from \"../useOnMount/useOnMount.js\";\nexport class Timeout {\n  static create() {\n    return new Timeout();\n  }\n  currentId = null;\n\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n  clear = () => {\n    if (this.currentId !== null) {\n      clearTimeout(this.currentId);\n      this.currentId = null;\n    }\n  };\n  disposeEffect = () => {\n    return this.clear;\n  };\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAAS,8BAA8B,GAAG,GAAG;AAC3C,MAAI,QAAQ,EAAG,QAAO,CAAC;AACvB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,CAAC,EAAE,eAAe,KAAK,GAAG,CAAC,GAAG;AACjD,QAAI,OAAO,EAAE,QAAQ,CAAC,EAAG;AACzB,MAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EACZ;AACA,SAAO;AACT;;;ACRA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,SAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUA,IAAGC,IAAG;AAC9F,WAAOD,GAAE,YAAYC,IAAGD;AAAA,EAC1B,GAAG,gBAAgB,GAAG,CAAC;AACzB;;;ACHA,SAAS,eAAe,GAAG,GAAG;AAC5B,IAAE,YAAY,OAAO,OAAO,EAAE,SAAS,GAAG,EAAE,UAAU,cAAc,GAAG,gBAAe,GAAG,CAAC;AAC5F;;;ACDA,IAAAE,qBAAsB;AACtB,IAAAC,gBAAkB;AAClB,uBAAqB;;;ACJrB,IAAO,iBAAQ;AAAA,EACb,UAAU;AACZ;;;ACFA,wBAAsB;AACf,IAAI,gBAAgB,OAAwC,kBAAAC,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,EACxH,OAAO,kBAAAA,QAAU;AAAA,EACjB,MAAM,kBAAAA,QAAU;AAAA,EAChB,QAAQ,kBAAAA,QAAU;AACpB,CAAC,EAAE,UAAU,CAAC,IAAI;AACX,IAAI,kBAAkB,OAAwC,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,EAC1H,OAAO,kBAAAA,QAAU;AAAA,EACjB,MAAM,kBAAAA,QAAU;AAAA,EAChB,QAAQ,kBAAAA,QAAU;AACpB,CAAC,GAAG,kBAAAA,QAAU,MAAM;AAAA,EAClB,OAAO,kBAAAA,QAAU;AAAA,EACjB,WAAW,kBAAAA,QAAU;AAAA,EACrB,aAAa,kBAAAA,QAAU;AAAA,EACvB,MAAM,kBAAAA,QAAU;AAAA,EAChB,UAAU,kBAAAA,QAAU;AAAA,EACpB,YAAY,kBAAAA,QAAU;AACxB,CAAC,CAAC,CAAC,IAAI;;;ACjBP,mBAAkB;AAClB,IAAO,iCAAQ,aAAAC,QAAM,cAAc,IAAI;;;ACDhC,IAAI,cAAc,SAASC,aAAY,MAAM;AAClD,SAAO,KAAK;AACd;;;AJOO,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,UAAU;AA6FrB,IAAI,cAA0B,SAAU,kBAAkB;AACxD,iBAAeC,aAAY,gBAAgB;AAE3C,WAASA,YAAW,OAAO,SAAS;AAClC,QAAI;AAEJ,YAAQ,iBAAiB,KAAK,MAAM,OAAO,OAAO,KAAK;AACvD,QAAI,cAAc;AAElB,QAAI,SAAS,eAAe,CAAC,YAAY,aAAa,MAAM,QAAQ,MAAM;AAC1E,QAAI;AACJ,UAAM,eAAe;AAErB,QAAI,MAAM,IAAI;AACZ,UAAI,QAAQ;AACV,wBAAgB;AAChB,cAAM,eAAe;AAAA,MACvB,OAAO;AACL,wBAAgB;AAAA,MAClB;AAAA,IACF,OAAO;AACL,UAAI,MAAM,iBAAiB,MAAM,cAAc;AAC7C,wBAAgB;AAAA,MAClB,OAAO;AACL,wBAAgB;AAAA,MAClB;AAAA,IACF;AAEA,UAAM,QAAQ;AAAA,MACZ,QAAQ;AAAA,IACV;AACA,UAAM,eAAe;AACrB,WAAO;AAAA,EACT;AAEA,EAAAA,YAAW,2BAA2B,SAAS,yBAAyB,MAAM,WAAW;AACvF,QAAI,SAAS,KAAK;AAElB,QAAI,UAAU,UAAU,WAAW,WAAW;AAC5C,aAAO;AAAA,QACL,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAkBA,MAAI,SAASA,YAAW;AAExB,SAAO,oBAAoB,SAAS,oBAAoB;AACtD,SAAK,aAAa,MAAM,KAAK,YAAY;AAAA,EAC3C;AAEA,SAAO,qBAAqB,SAAS,mBAAmB,WAAW;AACjE,QAAI,aAAa;AAEjB,QAAI,cAAc,KAAK,OAAO;AAC5B,UAAI,SAAS,KAAK,MAAM;AAExB,UAAI,KAAK,MAAM,IAAI;AACjB,YAAI,WAAW,YAAY,WAAW,SAAS;AAC7C,uBAAa;AAAA,QACf;AAAA,MACF,OAAO;AACL,YAAI,WAAW,YAAY,WAAW,SAAS;AAC7C,uBAAa;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAEA,SAAK,aAAa,OAAO,UAAU;AAAA,EACrC;AAEA,SAAO,uBAAuB,SAAS,uBAAuB;AAC5D,SAAK,mBAAmB;AAAA,EAC1B;AAEA,SAAO,cAAc,SAAS,cAAc;AAC1C,QAAIC,WAAU,KAAK,MAAM;AACzB,QAAI,MAAM,OAAO;AACjB,WAAO,QAAQ,SAASA;AAExB,QAAIA,YAAW,QAAQ,OAAOA,aAAY,UAAU;AAClD,aAAOA,SAAQ;AACf,cAAQA,SAAQ;AAEhB,eAASA,SAAQ,WAAW,SAAYA,SAAQ,SAAS;AAAA,IAC3D;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,SAAO,eAAe,SAAS,aAAa,UAAU,YAAY;AAChE,QAAI,aAAa,QAAQ;AACvB,iBAAW;AAAA,IACb;AAEA,QAAI,eAAe,MAAM;AAEvB,WAAK,mBAAmB;AAExB,UAAI,eAAe,UAAU;AAC3B,YAAI,KAAK,MAAM,iBAAiB,KAAK,MAAM,cAAc;AACvD,cAAI,OAAO,KAAK,MAAM,UAAU,KAAK,MAAM,QAAQ,UAAU,iBAAAC,QAAS,YAAY,IAAI;AAItF,cAAI,KAAM,aAAY,IAAI;AAAA,QAC5B;AAEA,aAAK,aAAa,QAAQ;AAAA,MAC5B,OAAO;AACL,aAAK,YAAY;AAAA,MACnB;AAAA,IACF,WAAW,KAAK,MAAM,iBAAiB,KAAK,MAAM,WAAW,QAAQ;AACnE,WAAK,SAAS;AAAA,QACZ,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,eAAe,SAAS,aAAa,UAAU;AACpD,QAAI,SAAS;AAEb,QAAI,QAAQ,KAAK,MAAM;AACvB,QAAI,YAAY,KAAK,UAAU,KAAK,QAAQ,aAAa;AAEzD,QAAI,QAAQ,KAAK,MAAM,UAAU,CAAC,SAAS,IAAI,CAAC,iBAAAA,QAAS,YAAY,IAAI,GAAG,SAAS,GACjF,YAAY,MAAM,CAAC,GACnB,iBAAiB,MAAM,CAAC;AAE5B,QAAI,WAAW,KAAK,YAAY;AAChC,QAAI,eAAe,YAAY,SAAS,SAAS,SAAS;AAG1D,QAAI,CAAC,YAAY,CAAC,SAAS,eAAO,UAAU;AAC1C,WAAK,aAAa;AAAA,QAChB,QAAQ;AAAA,MACV,GAAG,WAAY;AACb,eAAO,MAAM,UAAU,SAAS;AAAA,MAClC,CAAC;AACD;AAAA,IACF;AAEA,SAAK,MAAM,QAAQ,WAAW,cAAc;AAC5C,SAAK,aAAa;AAAA,MAChB,QAAQ;AAAA,IACV,GAAG,WAAY;AACb,aAAO,MAAM,WAAW,WAAW,cAAc;AAEjD,aAAO,gBAAgB,cAAc,WAAY;AAC/C,eAAO,aAAa;AAAA,UAClB,QAAQ;AAAA,QACV,GAAG,WAAY;AACb,iBAAO,MAAM,UAAU,WAAW,cAAc;AAAA,QAClD,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,cAAc,SAAS,cAAc;AAC1C,QAAI,SAAS;AAEb,QAAI,OAAO,KAAK,MAAM;AACtB,QAAI,WAAW,KAAK,YAAY;AAChC,QAAI,YAAY,KAAK,MAAM,UAAU,SAAY,iBAAAA,QAAS,YAAY,IAAI;AAE1E,QAAI,CAAC,QAAQ,eAAO,UAAU;AAC5B,WAAK,aAAa;AAAA,QAChB,QAAQ;AAAA,MACV,GAAG,WAAY;AACb,eAAO,MAAM,SAAS,SAAS;AAAA,MACjC,CAAC;AACD;AAAA,IACF;AAEA,SAAK,MAAM,OAAO,SAAS;AAC3B,SAAK,aAAa;AAAA,MAChB,QAAQ;AAAA,IACV,GAAG,WAAY;AACb,aAAO,MAAM,UAAU,SAAS;AAEhC,aAAO,gBAAgB,SAAS,MAAM,WAAY;AAChD,eAAO,aAAa;AAAA,UAClB,QAAQ;AAAA,QACV,GAAG,WAAY;AACb,iBAAO,MAAM,SAAS,SAAS;AAAA,QACjC,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,qBAAqB,SAAS,qBAAqB;AACxD,QAAI,KAAK,iBAAiB,MAAM;AAC9B,WAAK,aAAa,OAAO;AACzB,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAEA,SAAO,eAAe,SAAS,aAAa,WAAW,UAAU;AAI/D,eAAW,KAAK,gBAAgB,QAAQ;AACxC,SAAK,SAAS,WAAW,QAAQ;AAAA,EACnC;AAEA,SAAO,kBAAkB,SAAS,gBAAgB,UAAU;AAC1D,QAAI,SAAS;AAEb,QAAI,SAAS;AAEb,SAAK,eAAe,SAAU,OAAO;AACnC,UAAI,QAAQ;AACV,iBAAS;AACT,eAAO,eAAe;AACtB,iBAAS,KAAK;AAAA,MAChB;AAAA,IACF;AAEA,SAAK,aAAa,SAAS,WAAY;AACrC,eAAS;AAAA,IACX;AAEA,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,kBAAkB,SAAS,gBAAgBD,UAAS,SAAS;AAClE,SAAK,gBAAgB,OAAO;AAC5B,QAAI,OAAO,KAAK,MAAM,UAAU,KAAK,MAAM,QAAQ,UAAU,iBAAAC,QAAS,YAAY,IAAI;AACtF,QAAI,+BAA+BD,YAAW,QAAQ,CAAC,KAAK,MAAM;AAElE,QAAI,CAAC,QAAQ,8BAA8B;AACzC,iBAAW,KAAK,cAAc,CAAC;AAC/B;AAAA,IACF;AAEA,QAAI,KAAK,MAAM,gBAAgB;AAC7B,UAAI,QAAQ,KAAK,MAAM,UAAU,CAAC,KAAK,YAAY,IAAI,CAAC,MAAM,KAAK,YAAY,GAC3E,YAAY,MAAM,CAAC,GACnB,oBAAoB,MAAM,CAAC;AAE/B,WAAK,MAAM,eAAe,WAAW,iBAAiB;AAAA,IACxD;AAEA,QAAIA,YAAW,MAAM;AACnB,iBAAW,KAAK,cAAcA,QAAO;AAAA,IACvC;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,SAAS,KAAK,MAAM;AAExB,QAAI,WAAW,WAAW;AACxB,aAAO;AAAA,IACT;AAEA,QAAI,cAAc,KAAK,OACnBE,YAAW,YAAY,UACvB,MAAM,YAAY,IAClB,gBAAgB,YAAY,cAC5B,iBAAiB,YAAY,eAC7B,UAAU,YAAY,QACtB,SAAS,YAAY,OACrB,QAAQ,YAAY,MACpB,WAAW,YAAY,SACvB,kBAAkB,YAAY,gBAC9B,WAAW,YAAY,SACvB,cAAc,YAAY,YAC1B,aAAa,YAAY,WACzB,UAAU,YAAY,QACtB,aAAa,YAAY,WACzB,YAAY,YAAY,UACxB,WAAW,YAAY,SACvB,aAAa,8BAA8B,aAAa,CAAC,YAAY,MAAM,gBAAgB,iBAAiB,UAAU,SAAS,QAAQ,WAAW,kBAAkB,WAAW,cAAc,aAAa,UAAU,aAAa,YAAY,SAAS,CAAC;AAE3P;AAAA;AAAA,MAGE,cAAAC,QAAM,cAAc,+BAAuB,UAAU;AAAA,QACnD,OAAO;AAAA,MACT,GAAG,OAAOD,cAAa,aAAaA,UAAS,QAAQ,UAAU,IAAI,cAAAC,QAAM,aAAa,cAAAA,QAAM,SAAS,KAAKD,SAAQ,GAAG,UAAU,CAAC;AAAA;AAAA,EAEpI;AAEA,SAAOH;AACT,GAAE,cAAAI,QAAM,SAAS;AAEjB,WAAW,cAAc;AACzB,WAAW,YAAY,OAAwC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY7D,SAAS,mBAAAC,QAAU,MAAM;AAAA,IACvB,SAAS,OAAO,YAAY,cAAc,mBAAAA,QAAU,MAAM,SAAU,WAAW,KAAK,eAAe,UAAU,cAAc,QAAQ;AACjI,UAAI,QAAQ,UAAU,GAAG;AACzB,aAAO,mBAAAA,QAAU,WAAW,SAAS,mBAAmB,QAAQ,MAAM,cAAc,YAAY,UAAU,OAAO,EAAE,WAAW,KAAK,eAAe,UAAU,cAAc,MAAM;AAAA,IAClL;AAAA,EACF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBD,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,KAAK,YAAY,mBAAAA,QAAU,QAAQ,UAAU,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA,EAKzF,IAAI,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQd,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAazB,QAAQ,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAKlB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAKjB,MAAM,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BhB,SAAS,SAAS,QAAQ,OAAO;AAC/B,QAAI,KAAK;AACT,QAAI,CAAC,MAAM,eAAgB,MAAK,GAAG;AAEnC,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,WAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,IACjC;AAEA,WAAO,GAAG,MAAM,QAAQ,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,gBAAgB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU1B,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUnB,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUtB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrB,QAAQ,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrB,UAAU,mBAAAA,QAAU;AACtB,IAAI,CAAC;AAEL,SAAS,OAAO;AAAC;AAEjB,WAAW,eAAe;AAAA,EACxB,IAAI;AAAA,EACJ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,UAAU;AACZ;AACA,WAAW,YAAY;AACvB,WAAW,SAAS;AACpB,WAAW,WAAW;AACtB,WAAW,UAAU;AACrB,WAAW,UAAU;AACrB,IAAO,qBAAQ;;;AK7mBf,IAAAC,qBAAsB;;;ACGP,SAAR,SAA0B,SAAS,WAAW;AACnD,MAAI,QAAQ,UAAW,QAAO,CAAC,CAAC,aAAa,QAAQ,UAAU,SAAS,SAAS;AACjF,UAAQ,OAAO,QAAQ,UAAU,WAAW,QAAQ,aAAa,KAAK,QAAQ,MAAM,YAAY,GAAG,MAAM;AAC3G;;;ACDe,SAAR,SAA0B,SAAS,WAAW;AACnD,MAAI,QAAQ,UAAW,SAAQ,UAAU,IAAI,SAAS;AAAA,WAAW,CAAC,SAAS,SAAS,SAAS,EAAG,KAAI,OAAO,QAAQ,cAAc,SAAU,SAAQ,YAAY,QAAQ,YAAY,MAAM;AAAA,MAAe,SAAQ,aAAa,UAAU,QAAQ,aAAa,QAAQ,UAAU,WAAW,MAAM,MAAM,SAAS;AAChT;;;ACVA,SAAS,iBAAiB,WAAW,eAAe;AAClD,SAAO,UAAU,QAAQ,IAAI,OAAO,YAAY,gBAAgB,aAAa,GAAG,GAAG,IAAI,EAAE,QAAQ,QAAQ,GAAG,EAAE,QAAQ,cAAc,EAAE;AACxI;AASe,SAAR,YAA6B,SAAS,WAAW;AACtD,MAAI,QAAQ,WAAW;AACrB,YAAQ,UAAU,OAAO,SAAS;AAAA,EACpC,WAAW,OAAO,QAAQ,cAAc,UAAU;AAChD,YAAQ,YAAY,iBAAiB,QAAQ,WAAW,SAAS;AAAA,EACnE,OAAO;AACL,YAAQ,aAAa,SAAS,iBAAiB,QAAQ,aAAa,QAAQ,UAAU,WAAW,IAAI,SAAS,CAAC;AAAA,EACjH;AACF;;;AHbA,IAAAC,gBAAkB;AAKlB,IAAI,YAAY,SAASC,UAAS,MAAM,SAAS;AAC/C,SAAO,QAAQ,WAAW,QAAQ,MAAM,GAAG,EAAE,QAAQ,SAAU,GAAG;AAChE,WAAO,SAAY,MAAM,CAAC;AAAA,EAC5B,CAAC;AACH;AAEA,IAAIC,eAAc,SAASA,aAAY,MAAM,SAAS;AACpD,SAAO,QAAQ,WAAW,QAAQ,MAAM,GAAG,EAAE,QAAQ,SAAU,GAAG;AAChE,WAAO,YAAe,MAAM,CAAC;AAAA,EAC/B,CAAC;AACH;AAwEA,IAAI,iBAA6B,SAAU,kBAAkB;AAC3D,iBAAeC,gBAAe,gBAAgB;AAE9C,WAASA,iBAAgB;AACvB,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAC9E,UAAM,iBAAiB;AAAA,MACrB,QAAQ,CAAC;AAAA,MACT,OAAO,CAAC;AAAA,MACR,MAAM,CAAC;AAAA,IACT;AAEA,UAAM,UAAU,SAAU,WAAW,gBAAgB;AACnD,UAAI,wBAAwB,MAAM,iBAAiB,WAAW,cAAc,GACxE,OAAO,sBAAsB,CAAC,GAC9B,YAAY,sBAAsB,CAAC;AAEvC,YAAM,cAAc,MAAM,MAAM;AAEhC,YAAM,SAAS,MAAM,YAAY,WAAW,SAAS,MAAM;AAE3D,UAAI,MAAM,MAAM,SAAS;AACvB,cAAM,MAAM,QAAQ,WAAW,cAAc;AAAA,MAC/C;AAAA,IACF;AAEA,UAAM,aAAa,SAAU,WAAW,gBAAgB;AACtD,UAAI,yBAAyB,MAAM,iBAAiB,WAAW,cAAc,GACzE,OAAO,uBAAuB,CAAC,GAC/B,YAAY,uBAAuB,CAAC;AAExC,UAAI,OAAO,YAAY,WAAW;AAElC,YAAM,SAAS,MAAM,MAAM,QAAQ;AAEnC,UAAI,MAAM,MAAM,YAAY;AAC1B,cAAM,MAAM,WAAW,WAAW,cAAc;AAAA,MAClD;AAAA,IACF;AAEA,UAAM,YAAY,SAAU,WAAW,gBAAgB;AACrD,UAAI,yBAAyB,MAAM,iBAAiB,WAAW,cAAc,GACzE,OAAO,uBAAuB,CAAC,GAC/B,YAAY,uBAAuB,CAAC;AAExC,UAAI,OAAO,YAAY,WAAW;AAElC,YAAM,cAAc,MAAM,IAAI;AAE9B,YAAM,SAAS,MAAM,MAAM,MAAM;AAEjC,UAAI,MAAM,MAAM,WAAW;AACzB,cAAM,MAAM,UAAU,WAAW,cAAc;AAAA,MACjD;AAAA,IACF;AAEA,UAAM,SAAS,SAAU,WAAW;AAClC,UAAI,yBAAyB,MAAM,iBAAiB,SAAS,GACzD,OAAO,uBAAuB,CAAC;AAEnC,YAAM,cAAc,MAAM,QAAQ;AAElC,YAAM,cAAc,MAAM,OAAO;AAEjC,YAAM,SAAS,MAAM,QAAQ,MAAM;AAEnC,UAAI,MAAM,MAAM,QAAQ;AACtB,cAAM,MAAM,OAAO,SAAS;AAAA,MAC9B;AAAA,IACF;AAEA,UAAM,YAAY,SAAU,WAAW;AACrC,UAAI,yBAAyB,MAAM,iBAAiB,SAAS,GACzD,OAAO,uBAAuB,CAAC;AAEnC,YAAM,SAAS,MAAM,QAAQ,QAAQ;AAErC,UAAI,MAAM,MAAM,WAAW;AACzB,cAAM,MAAM,UAAU,SAAS;AAAA,MACjC;AAAA,IACF;AAEA,UAAM,WAAW,SAAU,WAAW;AACpC,UAAI,yBAAyB,MAAM,iBAAiB,SAAS,GACzD,OAAO,uBAAuB,CAAC;AAEnC,YAAM,cAAc,MAAM,MAAM;AAEhC,YAAM,SAAS,MAAM,QAAQ,MAAM;AAEnC,UAAI,MAAM,MAAM,UAAU;AACxB,cAAM,MAAM,SAAS,SAAS;AAAA,MAChC;AAAA,IACF;AAEA,UAAM,mBAAmB,SAAU,WAAW,gBAAgB;AAC5D,aAAO,MAAM,MAAM,UAAU,CAAC,MAAM,MAAM,QAAQ,SAAS,SAAS,IAClE,CAAC,WAAW,cAAc;AAAA,IAC9B;AAEA,UAAM,gBAAgB,SAAU,MAAM;AACpC,UAAI,aAAa,MAAM,MAAM;AAC7B,UAAI,qBAAqB,OAAO,eAAe;AAC/C,UAAI,SAAS,sBAAsB,aAAa,aAAa,MAAM;AACnE,UAAI,gBAAgB,qBAAqB,KAAK,SAAS,OAAO,WAAW,IAAI;AAC7E,UAAI,kBAAkB,qBAAqB,gBAAgB,YAAY,WAAW,OAAO,QAAQ;AACjG,UAAI,gBAAgB,qBAAqB,gBAAgB,UAAU,WAAW,OAAO,MAAM;AAC3F,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,eAAc;AAE3B,SAAO,WAAW,SAASF,UAAS,MAAM,MAAM,OAAO;AACrD,QAAI,YAAY,KAAK,cAAc,IAAI,EAAE,QAAQ,WAAW;AAE5D,QAAI,sBAAsB,KAAK,cAAc,OAAO,GAChD,gBAAgB,oBAAoB;AAExC,QAAI,SAAS,YAAY,UAAU,UAAU,eAAe;AAC1D,mBAAa,MAAM;AAAA,IACrB;AAIA,QAAI,UAAU,UAAU;AACtB,UAAI,KAAM,aAAY,IAAI;AAAA,IAC5B;AAEA,QAAI,WAAW;AACb,WAAK,eAAe,IAAI,EAAE,KAAK,IAAI;AAEnC,gBAAU,MAAM,SAAS;AAAA,IAC3B;AAAA,EACF;AAEA,SAAO,gBAAgB,SAAS,cAAc,MAAM,MAAM;AACxD,QAAI,wBAAwB,KAAK,eAAe,IAAI,GAChD,gBAAgB,sBAAsB,MACtC,kBAAkB,sBAAsB,QACxC,gBAAgB,sBAAsB;AAC1C,SAAK,eAAe,IAAI,IAAI,CAAC;AAE7B,QAAI,eAAe;AACjB,MAAAC,aAAY,MAAM,aAAa;AAAA,IACjC;AAEA,QAAI,iBAAiB;AACnB,MAAAA,aAAY,MAAM,eAAe;AAAA,IACnC;AAEA,QAAI,eAAe;AACjB,MAAAA,aAAY,MAAM,aAAa;AAAA,IACjC;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,cAAc,KAAK,OACnB,IAAI,YAAY,YAChB,QAAQ,8BAA8B,aAAa,CAAC,YAAY,CAAC;AAErE,WAAoB,cAAAE,QAAM,cAAc,oBAAY,SAAS,CAAC,GAAG,OAAO;AAAA,MACtE,SAAS,KAAK;AAAA,MACd,WAAW,KAAK;AAAA,MAChB,YAAY,KAAK;AAAA,MACjB,QAAQ,KAAK;AAAA,MACb,WAAW,KAAK;AAAA,MAChB,UAAU,KAAK;AAAA,IACjB,CAAC,CAAC;AAAA,EACJ;AAEA,SAAOD;AACT,GAAE,cAAAC,QAAM,SAAS;AAEjB,cAAc,eAAe;AAAA,EAC3B,YAAY;AACd;AACA,cAAc,YAAY,OAAwC,SAAS,CAAC,GAAG,mBAAW,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqEnG,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUZ,SAAS,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUnB,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUtB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUrB,QAAQ,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASlB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUrB,UAAU,mBAAAA,QAAU;AACtB,CAAC,IAAI,CAAC;;;AIxZN,IAAAC,qBAAsB;AACtB,IAAAC,gBAAkB;AAClB,IAAAC,oBAAqB;;;ACJrB,SAAS,uBAAuB,GAAG;AACjC,MAAI,WAAW,EAAG,OAAM,IAAI,eAAe,2DAA2D;AACtG,SAAO;AACT;;;ACCA,IAAAC,qBAAsB;AACtB,IAAAC,gBAAkB;;;ACLlB,IAAAC,gBAAuD;AAQhD,SAAS,gBAAgBC,WAAU,OAAO;AAC/C,MAAI,SAAS,SAASC,QAAO,OAAO;AAClC,WAAO,aAAS,8BAAe,KAAK,IAAI,MAAM,KAAK,IAAI;AAAA,EACzD;AAEA,MAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,MAAID,UAAU,wBAAS,IAAIA,WAAU,SAAU,GAAG;AAChD,WAAO;AAAA,EACT,CAAC,EAAE,QAAQ,SAAU,OAAO;AAE1B,WAAO,MAAM,GAAG,IAAI,OAAO,KAAK;AAAA,EAClC,CAAC;AACD,SAAO;AACT;AAmBO,SAAS,mBAAmB,MAAM,MAAM;AAC7C,SAAO,QAAQ,CAAC;AAChB,SAAO,QAAQ,CAAC;AAEhB,WAAS,eAAe,KAAK;AAC3B,WAAO,OAAO,OAAO,KAAK,GAAG,IAAI,KAAK,GAAG;AAAA,EAC3C;AAIA,MAAI,kBAAkB,uBAAO,OAAO,IAAI;AACxC,MAAI,cAAc,CAAC;AAEnB,WAAS,WAAW,MAAM;AACxB,QAAI,WAAW,MAAM;AACnB,UAAI,YAAY,QAAQ;AACtB,wBAAgB,OAAO,IAAI;AAC3B,sBAAc,CAAC;AAAA,MACjB;AAAA,IACF,OAAO;AACL,kBAAY,KAAK,OAAO;AAAA,IAC1B;AAAA,EACF;AAEA,MAAI;AACJ,MAAI,eAAe,CAAC;AAEpB,WAAS,WAAW,MAAM;AACxB,QAAI,gBAAgB,OAAO,GAAG;AAC5B,WAAK,IAAI,GAAG,IAAI,gBAAgB,OAAO,EAAE,QAAQ,KAAK;AACpD,YAAI,iBAAiB,gBAAgB,OAAO,EAAE,CAAC;AAC/C,qBAAa,gBAAgB,OAAO,EAAE,CAAC,CAAC,IAAI,eAAe,cAAc;AAAA,MAC3E;AAAA,IACF;AAEA,iBAAa,OAAO,IAAI,eAAe,OAAO;AAAA,EAChD;AAGA,OAAK,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACvC,iBAAa,YAAY,CAAC,CAAC,IAAI,eAAe,YAAY,CAAC,CAAC;AAAA,EAC9D;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,OAAO,MAAM,OAAO;AACnC,SAAO,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,IAAI,MAAM,MAAM,IAAI;AAC7D;AAEO,SAAS,uBAAuB,OAAO,UAAU;AACtD,SAAO,gBAAgB,MAAM,UAAU,SAAU,OAAO;AACtD,eAAO,4BAAa,OAAO;AAAA,MACzB,UAAU,SAAS,KAAK,MAAM,KAAK;AAAA,MACnC,IAAI;AAAA,MACJ,QAAQ,QAAQ,OAAO,UAAU,KAAK;AAAA,MACtC,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,MACpC,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,IACpC,CAAC;AAAA,EACH,CAAC;AACH;AACO,SAAS,oBAAoB,WAAW,kBAAkB,UAAU;AACzE,MAAI,mBAAmB,gBAAgB,UAAU,QAAQ;AACzD,MAAIA,YAAW,mBAAmB,kBAAkB,gBAAgB;AACpE,SAAO,KAAKA,SAAQ,EAAE,QAAQ,SAAU,KAAK;AAC3C,QAAI,QAAQA,UAAS,GAAG;AACxB,QAAI,KAAC,8BAAe,KAAK,EAAG;AAC5B,QAAI,UAAW,OAAO;AACtB,QAAI,UAAW,OAAO;AACtB,QAAI,YAAY,iBAAiB,GAAG;AACpC,QAAI,gBAAY,8BAAe,SAAS,KAAK,CAAC,UAAU,MAAM;AAE9D,QAAI,YAAY,CAAC,WAAW,YAAY;AAEtC,MAAAA,UAAS,GAAG,QAAI,4BAAa,OAAO;AAAA,QAClC,UAAU,SAAS,KAAK,MAAM,KAAK;AAAA,QACnC,IAAI;AAAA,QACJ,MAAM,QAAQ,OAAO,QAAQ,SAAS;AAAA,QACtC,OAAO,QAAQ,OAAO,SAAS,SAAS;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,CAAC,WAAW,WAAW,CAAC,WAAW;AAG5C,MAAAA,UAAS,GAAG,QAAI,4BAAa,OAAO;AAAA,QAClC,IAAI;AAAA,MACN,CAAC;AAAA,IACH,WAAW,WAAW,eAAW,8BAAe,SAAS,GAAG;AAI1D,MAAAA,UAAS,GAAG,QAAI,4BAAa,OAAO;AAAA,QAClC,UAAU,SAAS,KAAK,MAAM,KAAK;AAAA,QACnC,IAAI,UAAU,MAAM;AAAA,QACpB,MAAM,QAAQ,OAAO,QAAQ,SAAS;AAAA,QACtC,OAAO,QAAQ,OAAO,SAAS,SAAS;AAAA,MAC1C,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAOA;AACT;;;ADlIA,IAAI,SAAS,OAAO,UAAU,SAAU,KAAK;AAC3C,SAAO,OAAO,KAAK,GAAG,EAAE,IAAI,SAAU,GAAG;AACvC,WAAO,IAAI,CAAC;AAAA,EACd,CAAC;AACH;AAEA,IAAI,eAAe;AAAA,EACjB,WAAW;AAAA,EACX,cAAc,SAAS,aAAa,OAAO;AACzC,WAAO;AAAA,EACT;AACF;AAgBA,IAAI,mBAA+B,SAAU,kBAAkB;AAC7D,iBAAeE,kBAAiB,gBAAgB;AAEhD,WAASA,iBAAgB,OAAO,SAAS;AACvC,QAAI;AAEJ,YAAQ,iBAAiB,KAAK,MAAM,OAAO,OAAO,KAAK;AAEvD,QAAI,eAAe,MAAM,aAAa,KAAK,uBAAuB,KAAK,CAAC;AAGxE,UAAM,QAAQ;AAAA,MACZ,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,MACA;AAAA,MACA,aAAa;AAAA,IACf;AACA,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,iBAAgB;AAE7B,SAAO,oBAAoB,SAAS,oBAAoB;AACtD,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,MACZ,cAAc;AAAA,QACZ,YAAY;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,uBAAuB,SAAS,uBAAuB;AAC5D,SAAK,UAAU;AAAA,EACjB;AAEA,EAAAA,iBAAgB,2BAA2B,SAAS,yBAAyB,WAAW,MAAM;AAC5F,QAAI,mBAAmB,KAAK,UACxB,eAAe,KAAK,cACpB,cAAc,KAAK;AACvB,WAAO;AAAA,MACL,UAAU,cAAc,uBAAuB,WAAW,YAAY,IAAI,oBAAoB,WAAW,kBAAkB,YAAY;AAAA,MACvI,aAAa;AAAA,IACf;AAAA,EACF;AAGA,SAAO,eAAe,SAAS,aAAa,OAAO,MAAM;AACvD,QAAI,sBAAsB,gBAAgB,KAAK,MAAM,QAAQ;AAC7D,QAAI,MAAM,OAAO,oBAAqB;AAEtC,QAAI,MAAM,MAAM,UAAU;AACxB,YAAM,MAAM,SAAS,IAAI;AAAA,IAC3B;AAEA,QAAI,KAAK,SAAS;AAChB,WAAK,SAAS,SAAU,OAAO;AAC7B,YAAIC,YAAW,SAAS,CAAC,GAAG,MAAM,QAAQ;AAE1C,eAAOA,UAAS,MAAM,GAAG;AACzB,eAAO;AAAA,UACL,UAAUA;AAAA,QACZ;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,cAAc,KAAK,OACnB,YAAY,YAAY,WACxBC,gBAAe,YAAY,cAC3B,QAAQ,8BAA8B,aAAa,CAAC,aAAa,cAAc,CAAC;AAEpF,QAAI,eAAe,KAAK,MAAM;AAC9B,QAAID,YAAW,OAAO,KAAK,MAAM,QAAQ,EAAE,IAAIC,aAAY;AAC3D,WAAO,MAAM;AACb,WAAO,MAAM;AACb,WAAO,MAAM;AAEb,QAAI,cAAc,MAAM;AACtB,aAAoB,cAAAC,QAAM,cAAc,+BAAuB,UAAU;AAAA,QACvE,OAAO;AAAA,MACT,GAAGF,SAAQ;AAAA,IACb;AAEA,WAAoB,cAAAE,QAAM,cAAc,+BAAuB,UAAU;AAAA,MACvE,OAAO;AAAA,IACT,GAAgB,cAAAA,QAAM,cAAc,WAAW,OAAOF,SAAQ,CAAC;AAAA,EACjE;AAEA,SAAOD;AACT,GAAE,cAAAG,QAAM,SAAS;AAEjB,gBAAgB,YAAY,OAAwC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlE,WAAW,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAerB,UAAU,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpB,QAAQ,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjB,MAAM,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYhB,cAAc,mBAAAA,QAAU;AAC1B,IAAI,CAAC;AACL,gBAAgB,eAAe;AAC/B,IAAO,0BAAQ;;;AF1Kf,IAAI,qBAAiC,SAAU,kBAAkB;AAC/D,iBAAeC,oBAAmB,gBAAgB;AAElD,WAASA,qBAAoB;AAC3B,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,QAAQ,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACxF,YAAM,IAAI,IAAI,UAAU,IAAI;AAAA,IAC9B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,KAAK,CAAC,KAAK;AAE/E,UAAM,cAAc,WAAY;AAC9B,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,aAAK,KAAK,IAAI,UAAU,KAAK;AAAA,MAC/B;AAEA,aAAO,MAAM,gBAAgB,WAAW,GAAG,IAAI;AAAA,IACjD;AAEA,UAAM,iBAAiB,WAAY;AACjC,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,aAAK,KAAK,IAAI,UAAU,KAAK;AAAA,MAC/B;AAEA,aAAO,MAAM,gBAAgB,cAAc,GAAG,IAAI;AAAA,IACpD;AAEA,UAAM,gBAAgB,WAAY;AAChC,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,aAAK,KAAK,IAAI,UAAU,KAAK;AAAA,MAC/B;AAEA,aAAO,MAAM,gBAAgB,aAAa,GAAG,IAAI;AAAA,IACnD;AAEA,UAAM,aAAa,WAAY;AAC7B,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,aAAK,KAAK,IAAI,UAAU,KAAK;AAAA,MAC/B;AAEA,aAAO,MAAM,gBAAgB,UAAU,GAAG,IAAI;AAAA,IAChD;AAEA,UAAM,gBAAgB,WAAY;AAChC,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,aAAK,KAAK,IAAI,UAAU,KAAK;AAAA,MAC/B;AAEA,aAAO,MAAM,gBAAgB,aAAa,GAAG,IAAI;AAAA,IACnD;AAEA,UAAM,eAAe,WAAY;AAC/B,eAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,aAAK,KAAK,IAAI,UAAU,KAAK;AAAA,MAC/B;AAEA,aAAO,MAAM,gBAAgB,YAAY,GAAG,IAAI;AAAA,IAClD;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,mBAAkB;AAE/B,SAAO,kBAAkB,SAAS,gBAAgB,SAAS,KAAK,cAAc;AAC5E,QAAI;AAEJ,QAAIC,YAAW,KAAK,MAAM;AAC1B,QAAI,QAAQ,cAAAC,QAAM,SAAS,QAAQD,SAAQ,EAAE,GAAG;AAChD,QAAI,MAAM,MAAM,OAAO,EAAG,EAAC,eAAe,MAAM,OAAO,OAAO,EAAE,MAAM,cAAc,YAAY;AAEhG,QAAI,KAAK,MAAM,OAAO,GAAG;AACvB,UAAI,YAAY,MAAM,MAAM,UAAU,SAAY,kBAAAE,QAAS,YAAY,IAAI;AAC3E,WAAK,MAAM,OAAO,EAAE,SAAS;AAAA,IAC/B;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,cAAc,KAAK,OACnBF,YAAW,YAAY,UACvB,SAAS,YAAY,IACrB,QAAQ,8BAA8B,aAAa,CAAC,YAAY,IAAI,CAAC;AAEzE,QAAI,wBAAwB,cAAAC,QAAM,SAAS,QAAQD,SAAQ,GACvD,QAAQ,sBAAsB,CAAC,GAC/B,SAAS,sBAAsB,CAAC;AAEpC,WAAO,MAAM;AACb,WAAO,MAAM;AACb,WAAO,MAAM;AACb,WAAO,MAAM;AACb,WAAO,MAAM;AACb,WAAO,MAAM;AACb,WAAoB,cAAAC,QAAM,cAAc,yBAAiB,OAAO,SAAS,cAAAA,QAAM,aAAa,OAAO;AAAA,MACjG,KAAK;AAAA,MACL,SAAS,KAAK;AAAA,MACd,YAAY,KAAK;AAAA,MACjB,WAAW,KAAK;AAAA,IAClB,CAAC,IAAI,cAAAA,QAAM,aAAa,QAAQ;AAAA,MAC9B,KAAK;AAAA,MACL,SAAS,KAAK;AAAA,MACd,YAAY,KAAK;AAAA,MACjB,WAAW,KAAK;AAAA,IAClB,CAAC,CAAC;AAAA,EACJ;AAEA,SAAOF;AACT,GAAE,cAAAE,QAAM,SAAS;AAEjB,kBAAkB,YAAY,OAAwC;AAAA,EACpE,IAAI,mBAAAE,QAAU,KAAK;AAAA,EACnB,UAAU,SAAS,SAAS,OAAO,UAAU;AAC3C,QAAI,cAAAF,QAAM,SAAS,MAAM,MAAM,QAAQ,CAAC,MAAM,EAAG,QAAO,IAAI,MAAM,MAAO,WAAW,8CAA+C;AACnI,WAAO;AAAA,EACT;AACF,IAAI,CAAC;;;AIlIL,IAAAG,gBAAkB;AAClB,IAAAC,qBAAsB;AAHtB,IAAI;AAAJ,IAAmB;AAOnB,SAAS,qBAAqB,aAAa,aAAa;AACtD,MAAI,gBAAgB,YAAa,QAAO;AAExC,MAAI,cAAAC,QAAM,eAAe,WAAW,KAAK,cAAAA,QAAM,eAAe,WAAW,KAAK,YAAY,OAAO,QAAQ,YAAY,QAAQ,YAAY,KAAK;AAC5I,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAOO,IAAI,QAAQ;AAAA,EACjB,KAAK;AAAA,EACL,IAAI;AACN;AAEA,IAAI,WAAW,SAASC,UAAS,SAAS,MAAM,IAAI;AAClD,SAAO,WAAY;AACjB,QAAI;AAEJ,YAAQ,MAAM,IAAI,MAAM,iBAAiB,QAAQ,OAAO,IAAI,EAAE,MAAM,gBAAgB,SAAS;AAC7F,OAAG;AAAA,EACL;AACF;AAEA,IAAI,gBAAgB,gBAAgB,CAAC,GAAG,cAAc,MAAM,GAAG,IAAI,SAAU,MAAM;AACjF,MAAI,UAAU,KAAK,SACf,cAAc,KAAK;AACvB,SAAO,cAAAD,QAAM,aAAa,SAAS;AAAA,IACjC,IAAI;AAAA,IACJ,UAAU,SAAS,SAAS,YAAY,WAAY;AAClD,kBAAY,UAAU,IAAI;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG,cAAc,MAAM,EAAE,IAAI,SAAU,OAAO;AAC5C,MAAI,UAAU,MAAM,SAChB,cAAc,MAAM,aACpBE,YAAW,MAAM;AACrB,SAAO,CAAC,SAAS,cAAAF,QAAM,aAAaE,WAAU;AAAA,IAC5C,IAAI;AAAA,IACJ,WAAW,SAASA,WAAU,aAAa,WAAY;AACrD,kBAAY,QAAQ;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,CAAC;AACJ,GAAG;AACH,IAAI,gBAAgB,gBAAgB,CAAC,GAAG,cAAc,MAAM,GAAG,IAAI,SAAU,OAAO;AAClF,MAAIA,YAAW,MAAM,UACjB,cAAc,MAAM;AACxB,SAAO,cAAAF,QAAM,aAAaE,WAAU;AAAA,IAClC,IAAI;AAAA,IACJ,WAAW,SAASA,WAAU,aAAa,WAAY;AACrD,kBAAY,SAAS,cAAAF,QAAM,aAAaE,WAAU;AAAA,QAChD,IAAI;AAAA,MACN,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH,CAAC;AACH,GAAG,cAAc,MAAM,EAAE,IAAI,SAAU,OAAO;AAC5C,MAAI,UAAU,MAAM,SAChBA,YAAW,MAAM,UACjB,cAAc,MAAM;AACxB,SAAO,CAAC,cAAAF,QAAM,aAAa,SAAS;AAAA,IAClC,IAAI;AAAA,IACJ,UAAU,SAAS,SAAS,YAAY,WAAY;AAClD,kBAAY,SAAS,cAAAA,QAAM,aAAaE,WAAU;AAAA,QAChD,IAAI;AAAA,MACN,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH,CAAC,GAAG,cAAAF,QAAM,aAAaE,WAAU;AAAA,IAC/B,IAAI;AAAA,EACN,CAAC,CAAC;AACJ,GAAG;AAsDH,IAAI,oBAAgC,SAAU,kBAAkB;AAC9D,iBAAeC,mBAAkB,gBAAgB;AAEjD,WAASA,oBAAmB;AAC1B,QAAI;AAEJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,YAAQ,iBAAiB,KAAK,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,KAAK;AAC9E,UAAM,QAAQ;AAAA,MACZ,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AACA,UAAM,WAAW;AAEjB,UAAM,cAAc,SAAU,QAAQ,SAAS;AAC7C,UAAI,YAAY,QAAQ;AACtB,kBAAU,MAAM,MAAM;AAAA,MACxB;AAEA,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,kBAAiB;AAE9B,SAAO,oBAAoB,SAAS,oBAAoB;AACtD,SAAK,WAAW;AAAA,EAClB;AAEA,EAAAA,kBAAiB,2BAA2B,SAAS,yBAAyB,OAAO,OAAO;AAC1F,QAAI,MAAM,YAAY,MAAM;AAC1B,aAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,IACF;AAEA,QAAI,MAAM,WAAW,YAAY,MAAM,SAAS,MAAM,IAAI;AACxD,aAAO;AAAA,QACL,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,QAAI,MAAM,WAAW,qBAAqB,MAAM,SAAS,MAAM,QAAQ,GAAG;AACxE,aAAO;AAAA,QACL,QAAQ;AAAA,MACV;AAAA,IACF;AAEA,WAAO;AAAA,MACL,SAAS,cAAAH,QAAM,aAAa,MAAM,UAAU;AAAA,QAC1C,IAAI;AAAA,MACN,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,cAAc,KAAK,OACnBE,YAAW,YAAY,UACvB,OAAO,YAAY,MACnB,cAAc,KAAK,OACnB,SAAS,YAAY,QACrB,UAAU,YAAY;AAC1B,QAAI,OAAO;AAAA,MACT,UAAUA;AAAA,MACV;AAAA,MACA,aAAa,KAAK;AAAA,MAClB;AAAA,IACF;AACA,QAAI;AAEJ,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH,oBAAY,aAAa,IAAI,EAAE,IAAI;AACnC;AAAA,MAEF,KAAK;AACH,oBAAY,aAAa,IAAI,EAAE,IAAI;AACnC;AAAA,MAEF,KAAK;AACH,oBAAY;AAAA,IAChB;AAEA,WAAoB,cAAAF,QAAM,cAAc,+BAAuB,UAAU;AAAA,MACvE,OAAO;AAAA,QACL,YAAY,CAAC,KAAK;AAAA,MACpB;AAAA,IACF,GAAG,SAAS;AAAA,EACd;AAEA,SAAOG;AACT,GAAE,cAAAH,QAAM,SAAS;AAEjB,iBAAiB,YAAY,OAAwC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnE,MAAM,mBAAAI,QAAU,MAAM,CAAC,MAAM,IAAI,MAAM,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA,EAK3C,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,UAAU,CAAC;AAC9D,IAAI,CAAC;AACL,iBAAiB,eAAe;AAAA,EAC9B,MAAM,MAAM;AACd;;;AC7PA,IAAAC,SAAuB;AACvB,IAAM,gBAAgB,CAAC;AASR,SAAR,WAA4B,MAAM,SAAS;AAChD,QAAM,MAAY,cAAO,aAAa;AACtC,MAAI,IAAI,YAAY,eAAe;AACjC,QAAI,UAAU,KAAK,OAAO;AAAA,EAC5B;AACA,SAAO;AACT;;;AChBA,IAAAC,SAAuB;AACvB,IAAM,QAAQ,CAAC;AAKA,SAAR,WAA4B,IAAI;AAGrC,EAAM,iBAAU,IAAI,KAAK;AAE3B;;;ACTO,IAAM,UAAN,MAAM,SAAQ;AAAA,EACnB,OAAO,SAAS;AACd,WAAO,IAAI,SAAQ;AAAA,EACrB;AAAA,EACA,YAAY;AAAA;AAAA;AAAA;AAAA,EAKZ,MAAM,OAAO,IAAI;AACf,SAAK,MAAM;AACX,SAAK,YAAY,WAAW,MAAM;AAChC,WAAK,YAAY;AACjB,SAAG;AAAA,IACL,GAAG,KAAK;AAAA,EACV;AAAA,EACA,QAAQ,MAAM;AACZ,QAAI,KAAK,cAAc,MAAM;AAC3B,mBAAa,KAAK,SAAS;AAC3B,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,KAAK;AAAA,EACd;AACF;AACe,SAAR,aAA8B;AACnC,QAAMC,WAAU,WAAW,QAAQ,MAAM,EAAE;AAC3C,aAAWA,SAAQ,aAAa;AAChC,SAAOA;AACT;", "names": ["t", "e", "import_prop_types", "import_react", "PropTypes", "React", "forceReflow", "Transition", "timeout", "ReactDOM", "children", "React", "PropTypes", "import_prop_types", "import_react", "addClass", "removeClass", "CSSTransition", "React", "PropTypes", "import_prop_types", "import_react", "import_react_dom", "import_prop_types", "import_react", "import_react", "children", "mapper", "TransitionGroup", "children", "childFactory", "React", "PropTypes", "ReplaceTransition", "children", "React", "ReactDOM", "PropTypes", "import_react", "import_prop_types", "React", "callHook", "children", "SwitchTransition", "PropTypes", "React", "React", "timeout"]}