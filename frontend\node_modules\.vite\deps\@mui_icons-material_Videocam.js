"use client";
import "./chunk-C6WWHQR7.js";
import "./chunk-N2GVGIUZ.js";
import "./chunk-UUVOTGVA.js";
import {
  createSvgIcon
} from "./chunk-T3XEQW4B.js";
import "./chunk-E4MPIUBI.js";
import "./chunk-APDN7BYG.js";
import "./chunk-X34AUYXR.js";
import "./chunk-VOMKMCJM.js";
import "./chunk-JMU42XZZ.js";
import "./chunk-ZDQEUMQY.js";
import "./chunk-4YCHEYSE.js";
import {
  require_jsx_runtime
} from "./chunk-OBYCLIUT.js";
import "./chunk-BQYK6RGN.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/@mui/icons-material/esm/Videocam.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var Videocam_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11z"
}), "Videocam");
export {
  Videocam_default as default
};
//# sourceMappingURL=@mui_icons-material_Videocam.js.map
